// Tipos mejorados para eventos de Browser-Use

// Tipos base
export interface BaseEvent {
  id: string | object;
  event_id: string;
  correlation_id: string;
  session_correlation_id?: string | null;
  task_correlation_id?: string;
  event_type: string;
  timestamp: string;
  user_id: string;
  device_id: string;
  data: EventData;
}

// Datos específicos por tipo de evento
export interface EventData {
  event_type: string;
  event_schema: string;
  event_timeout?: number;
  event_id: string;
  event_path?: string[];
  event_parent_id?: string | null;
  event_created_at: string;
  event_processed_at?: string | null;
  id: string;
  user_id: string;
  device_id: string;
  created_at: string;
  agent_task_id?: string;
  step?: number;
  evaluation_previous_goal?: string;
  memory?: string;
  next_goal?: string;
  actions?: Action[];
  screenshot_url?: string;
  browser_session_id?: string;
  browser_state?: BrowserState;
  llm_model?: string;
  task?: string;
  agent_state?: string;
  url?: string;
  file_path?: string;
  file_type?: string;
  file_size?: number;
  action_type?: string;
  viewport?: Viewport;
}

// Tipos específicos de eventos
export interface CreateAgentStepEvent extends BaseEvent {
  event_type: 'CreateAgentStepEvent';
  data: EventData & {
    agent_task_id: string;
    step: number;
    evaluation_previous_goal?: string;
    memory?: string;
    next_goal?: string;
    actions?: Action[];
    screenshot_url?: string;
  };
}

export interface SessionStartEvent extends BaseEvent {
  event_type: 'SessionStartEvent';
  data: EventData & {
    browser_session_id: string;
    browser_state?: BrowserState;
  };
}

export interface TaskCreateEvent extends BaseEvent {
  event_type: 'TaskCreateEvent';
  data: EventData & {
    agent_task_id: string;
    llm_model?: string;
    task?: string;
    agent_state?: string;
  };
}

export interface OutputFileEvent extends BaseEvent {
  event_type: 'OutputFileEvent';
  data: EventData & {
    file_path: string;
    file_type?: string;
    file_size?: number;
  };
}

export interface UpdateTaskEvent extends BaseEvent {
  event_type: 'UpdateTaskEvent';
  data: EventData & {
    agent_task_id: string;
    task?: string;
    agent_state?: string;
  };
}

// Tipos auxiliares
export interface Action {
  done?: {
    text: string;
    success: boolean;
    files_to_display?: string[] | null;
  };
  click?: {
    coordinate: [number, number];
    element_id?: string;
  };
  type?: {
    text: string;
    element_id?: string;
  };
  scroll?: {
    direction: 'up' | 'down' | 'left' | 'right';
    amount?: number;
  };
  navigate?: {
    url: string;
  };
  wait?: {
    seconds: number;
  };
}

export interface BrowserState {
  viewport?: Viewport;
  user_agent?: string;
  url?: string;
  title?: string;
  cookies?: Cookie[];
}

export interface Viewport {
  width: number;
  height: number;
}

export interface Cookie {
  name: string;
  value: string;
  domain?: string;
  path?: string;
  expires?: string;
  httpOnly?: boolean;
  secure?: boolean;
}

// Union type para todos los eventos
export type BrowserUseEvent = 
  | CreateAgentStepEvent
  | SessionStartEvent
  | TaskCreateEvent
  | OutputFileEvent
  | UpdateTaskEvent
  | BaseEvent; // Fallback para eventos no tipados específicamente

// Tipos para estadísticas
export interface EventStats {
  total_events: number;
  sessions: number;
  tasks: number;
  steps: number;
  output_files: number;
  unique_users: number;
  unique_devices: number;
  events_per_hour: number;
}

// Tipos para filtros
export interface EventFilters {
  searchTerm?: string;
  type?: string;
  userId?: string;
  deviceId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Tipos para la respuesta de la API
export interface EventsResponse {
  status: 'success' | 'error';
  events: BrowserUseEvent[];
  total_count?: number;
  page?: number;
  per_page?: number;
  real_user_ids_applied?: number;
}

// Tipos para contexto de eventos
export interface EventContext {
  action?: string;
  task?: string;
  step?: string;
  viewport?: string;
  file?: string;
  url?: string;
}

// Helpers para type guards
export function isCreateAgentStepEvent(event: BrowserUseEvent): event is CreateAgentStepEvent {
  return event.event_type === 'CreateAgentStepEvent';
}

export function isSessionStartEvent(event: BrowserUseEvent): event is SessionStartEvent {
  return event.event_type === 'SessionStartEvent';
}

export function isTaskCreateEvent(event: BrowserUseEvent): event is TaskCreateEvent {
  return event.event_type === 'TaskCreateEvent';
}

export function isOutputFileEvent(event: BrowserUseEvent): event is OutputFileEvent {
  return event.event_type === 'OutputFileEvent';
}

export function isUpdateTaskEvent(event: BrowserUseEvent): event is UpdateTaskEvent {
  return event.event_type === 'UpdateTaskEvent';
}

// Constantes para tipos de eventos
export const EVENT_TYPES = {
  CREATE_AGENT_STEP: 'CreateAgentStepEvent',
  SESSION_START: 'SessionStartEvent',
  TASK_CREATE: 'TaskCreateEvent',
  OUTPUT_FILE: 'OutputFileEvent',
  UPDATE_TASK: 'UpdateTaskEvent',
} as const;

export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];