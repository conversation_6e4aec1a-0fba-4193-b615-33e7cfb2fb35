"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Copy, Calendar, User, Monitor, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { formatTimestamp, formatEventType, getEventIcon, getEventColor, formatUserDisplayName, getUserColor, formatDeviceDisplayName, getEventContext } from './utils';
import { BrowserUseEvent } from '@/types/browser-use-events';

interface EventDetailsModalProps {
  event: BrowserUseEvent | null;
  isOpen: boolean;
  onClose: () => void;
}

export function EventDetailsModal({ event, isOpen, onClose }: EventDetailsModalProps) {
  const { toast } = useToast();

  if (!event) return null;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copiado",
        description: "Contenido copiado al portapapeles",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo copiar al portapapeles",
        variant: "destructive",
      });
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'session': return 'bg-blue-100 text-blue-800';
      case 'task': return 'bg-green-100 text-green-800';
      case 'step': return 'bg-yellow-100 text-yellow-800';
      case 'output_file': return 'bg-purple-100 text-purple-800';
      case 'update_task': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatEventData = (data: any) => {
    return JSON.stringify(data, null, 2);
  };

  const getEventSpecificFields = (event: BrowserUseEvent) => {
    const fields: { label: string; value: any }[] = [];
    
    switch (event.type) {
      case 'session':
        if (event.data.browser_session_id) fields.push({ label: 'Session ID', value: event.data.browser_session_id });
        if (event.data.browser_state?.viewport) fields.push({ label: 'Viewport', value: `${event.data.browser_state.viewport.width}x${event.data.browser_state.viewport.height}` });
        if (event.data.browser_state?.user_agent) fields.push({ label: 'User Agent', value: event.data.browser_state.user_agent });
        break;
      case 'task':
        if (event.data.agent_task_id) fields.push({ label: 'Task ID', value: event.data.agent_task_id });
        if (event.data.llm_model) fields.push({ label: 'LLM Model', value: event.data.llm_model });
        if (event.data.task) fields.push({ label: 'Task Description', value: event.data.task });
        if (event.data.agent_state) fields.push({ label: 'Agent State', value: event.data.agent_state });
        break;
      case 'step':
        if (event.data.agent_task_id) fields.push({ label: 'Task ID', value: event.data.agent_task_id });
        if (event.data.step) fields.push({ label: 'Step Number', value: event.data.step });
        if (event.data.next_goal) fields.push({ label: 'Next Goal', value: event.data.next_goal });
        if (event.data.url) fields.push({ label: 'URL', value: event.data.url });
        break;
      case 'output_file':
        if (event.data.file_path) fields.push({ label: 'File Path', value: event.data.file_path });
        if (event.data.file_type) fields.push({ label: 'File Type', value: event.data.file_type });
        if (event.data.file_size) fields.push({ label: 'File Size', value: `${event.data.file_size} bytes` });
        break;
    }
    
    return fields;
  };

  const specificFields = getEventSpecificFields(event);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span className="text-xl">{getEventIcon(event.type)}</span>
            <div>
              <span className="text-lg font-semibold">{formatEventType(event.type)}</span>
              <Badge className={getEventColor(event.type)} style={{marginLeft: '8px'}}>
                {event.type}
              </Badge>
            </div>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Event Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Timestamp
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{formatTimestamp(event.timestamp)}</p>
              </CardContent>
            </Card>
            
            {event.user_id && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Usuario
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Badge className={getUserColor(event.user_id)}>
                    {formatUserDisplayName(event.user_id)}
                  </Badge>
                </CardContent>
              </Card>
            )}
            
            {event.device_id && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    Dispositivo
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{formatDeviceDisplayName(event.device_id)}</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Event-specific fields */}
          {specificFields.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Campos Específicos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {specificFields.map((field, index) => (
                    <div key={index} className="space-y-1">
                      <label className="text-xs font-medium text-muted-foreground">
                        {field.label}
                      </label>
                      <p className="text-sm bg-muted p-2 rounded text-wrap break-all">
                        {typeof field.value === 'string' ? field.value : JSON.stringify(field.value)}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Event Data Tabs */}
          <Tabs defaultValue="formatted" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="formatted">Vista Formateada</TabsTrigger>
              <TabsTrigger value="raw">Datos Raw</TabsTrigger>
            </TabsList>
            
            <TabsContent value="formatted" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-sm">Datos del Evento (Formateado)</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(formatEventData(event.data))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copiar
                  </Button>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[300px]">
                    <pre className="text-xs bg-muted p-4 rounded overflow-auto">
                      {formatEventData(event.data)}
                    </pre>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="raw" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-sm">Evento Completo (Raw)</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(formatEventData(event))}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copiar
                  </Button>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[300px]">
                    <pre className="text-xs bg-muted p-4 rounded overflow-auto">
                      {formatEventData(event)}
                    </pre>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}