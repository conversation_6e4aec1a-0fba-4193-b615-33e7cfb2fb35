# Implementación de User IDs Reales en Browser-Use Events

## Situación Actual

Actualmente, el sistema de eventos de browser-use utiliza un **user_id temporal** predefinido:

```python
# En /libs/browser_use/sync/auth.py
TEMP_USER_ID = '99999999-9999-9999-9999-999999999999'
```

Este ID se usa cuando no hay autenticación real configurada, lo que resulta en que todos los eventos aparezcan con el mismo user_id genérico.

## Opciones para Implementar User IDs Reales

### 1. **Autenticación a Nivel de API** (Recomendado)

#### Implementación en el Endpoint

Modificar el endpoint `/api/v1/events` para extraer el user_id de diferentes fuentes:

```python
# En src/api/browser_use_sync_routes.py

async def extract_user_id_from_request(request: Request, authorization: Optional[str] = None) -> str:
    """Extraer user_id real de diferentes fuentes."""
    
    # 1. Desde header de autorización (JWT, API Key, etc.)
    if authorization:
        try:
            # Decodificar JWT o validar API key
            user_id = decode_jwt_or_api_key(authorization)
            if user_id:
                return user_id
        except Exception:
            pass
    
    # 2. Desde headers personalizados
    user_id = request.headers.get('X-User-ID')
    if user_id:
        return user_id
    
    # 3. Desde query parameters
    user_id = request.query_params.get('user_id')
    if user_id:
        return user_id
    
    # 4. Desde IP + User Agent (fingerprinting básico)
    source_ip = request.client.host if request.client else 'unknown'
    user_agent = request.headers.get('user-agent', 'unknown')
    return f"user_{hash(f'{source_ip}_{user_agent}')}"

# Modificar el endpoint principal
@router.post("/api/v1/events")
async def receive_events(
    event_batch: EventBatch,
    request: Request,
    authorization: Optional[str] = Header(None)
):
    # Extraer user_id real
    real_user_id = await extract_user_id_from_request(request, authorization)
    
    for event_data in event_batch.events:
        # Sobrescribir el user_id temporal con el real
        event_data['user_id'] = real_user_id
        # ... resto del procesamiento
```

### 2. **Configuración por Variables de Entorno**

#### Crear un Sistema de Configuración de Usuario

```python
# Nuevo archivo: src/config/user_config.py

import os
import uuid
from pathlib import Path

class UserConfig:
    """Configuración de usuario para browser-use."""
    
    def __init__(self):
        self.config_file = Path.home() / '.qak' / 'user_config.json'
        self._ensure_config_exists()
    
    def get_user_id(self) -> str:
        """Obtener user_id configurado o generar uno único."""
        
        # 1. Desde variable de entorno
        env_user_id = os.getenv('QAK_USER_ID')
        if env_user_id:
            return env_user_id
        
        # 2. Desde archivo de configuración
        if self.config_file.exists():
            try:
                import json
                with open(self.config_file) as f:
                    config = json.load(f)
                    return config.get('user_id')
            except Exception:
                pass
        
        # 3. Generar y guardar uno nuevo
        new_user_id = f"user_{uuid.uuid4().hex[:8]}"
        self._save_user_id(new_user_id)
        return new_user_id
    
    def set_user_id(self, user_id: str) -> None:
        """Configurar user_id manualmente."""
        self._save_user_id(user_id)
    
    def _save_user_id(self, user_id: str) -> None:
        """Guardar user_id en archivo de configuración."""
        import json
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        config = {'user_id': user_id, 'created_at': str(datetime.utcnow())}
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
```

### 3. **Integración con Sistema de Autenticación Existente**

#### Si tienes un sistema de login web

```python
# En el frontend (web/src/app/browser-use-events/page.tsx)

// Agregar autenticación de usuario
const [currentUser, setCurrentUser] = useState<string | null>(null);

useEffect(() => {
  // Obtener usuario actual del sistema de auth
  const user = getCurrentUser(); // Tu función de auth
  setCurrentUser(user?.id || 'anonymous');
}, []);

// Filtrar eventos por usuario actual
const userEvents = filteredEvents.filter(event => 
  event.user_id === currentUser || currentUser === 'admin'
);
```

### 4. **Sistema de Sesiones por Proyecto**

#### Asociar user_id con proyectos específicos

```python
# En src/core/browser_pool.py

class BrowserConfig:
    def __init__(self, project_id: str = None, user_id: str = None, **kwargs):
        self.project_id = project_id
        self.user_id = user_id or self._generate_project_user_id(project_id)
    
    def _generate_project_user_id(self, project_id: str) -> str:
        """Generar user_id basado en el proyecto."""
        if project_id:
            return f"project_{project_id}_user"
        return f"user_{uuid.uuid4().hex[:8]}"
```

## Implementación Recomendada (Paso a Paso)

### Paso 1: Crear Sistema de Configuración de Usuario

```bash
# Crear comando CLI para configurar usuario
python -m qak config user --set-id "mi_usuario_real"
```

### Paso 2: Modificar el Endpoint de Eventos

1. Agregar extracción de user_id real en `browser_use_sync_routes.py`
2. Implementar múltiples fuentes de user_id (headers, env vars, config)
3. Mantener fallback al sistema actual

### Paso 3: Actualizar la Interfaz Web

1. Agregar selector de usuario en la interfaz
2. Mostrar nombres de usuario reales en lugar de UUIDs
3. Implementar filtros por usuario específico

### Paso 4: Documentar el Sistema

1. Crear guía de configuración de usuarios
2. Documentar opciones de autenticación
3. Proporcionar ejemplos de uso

## Beneficios de la Implementación

### 1. **Identificación Real de Usuarios**
- Nombres legibles en lugar de UUIDs
- Trazabilidad de acciones por usuario
- Mejor análisis de uso

### 2. **Seguridad Mejorada**
- Control de acceso por usuario
- Auditoría de acciones
- Aislamiento de datos

### 3. **Experiencia de Usuario**
- Interfaz más clara y comprensible
- Filtros más útiles
- Mejor organización de eventos

### 4. **Escalabilidad**
- Soporte para múltiples usuarios
- Integración con sistemas existentes
- Flexibilidad de configuración

## Ejemplo de Configuración

```bash
# Configurar usuario via CLI
export QAK_USER_ID="juan.perez"

# O via archivo de configuración
echo '{"user_id": "juan.perez", "display_name": "Juan Pérez"}' > ~/.qak/user_config.json

# O via header HTTP
curl -H "X-User-ID: juan.perez" -H "Authorization: Bearer token123" \
  http://localhost:8000/api/v1/events
```

## Migración de Datos Existentes

```python
# Script para migrar eventos existentes
async def migrate_existing_events():
    """Migrar eventos con TEMP_USER_ID a user_ids reales."""
    
    # Buscar eventos con user_id temporal
    temp_events = await event_repo.find_many({
        'user_id': '99999999-9999-9999-9999-999999999999'
    })
    
    # Asignar user_ids basados en IP, timestamp, etc.
    for event in temp_events:
        new_user_id = generate_user_id_from_metadata(event)
        await event_repo.update(event.id, {'user_id': new_user_id})
```

## Conclusión

La implementación de user IDs reales mejorará significativamente la usabilidad y trazabilidad del sistema de eventos. La opción recomendada es comenzar con un sistema de configuración simple y luego expandir hacia autenticación más robusta según las necesidades del proyecto.