#!/usr/bin/env python3
"""
Script de configuración para establecer User IDs reales en QAK Browser-Use Events.

Este script permite a los usuarios configurar fácilmente sus IDs de usuario reales
en lugar del TEMP_USER_ID genérico de browser-use.

Uso:
    python configure_user_id.py
    python configure_user_id.py --user-id "mi_usuario"
    python configure_user_id.py --email "<EMAIL>"
    python configure_user_id.py --clear
"""

import argparse
import os
import sys
from pathlib import Path

# Agregar el directorio src al path para importar módulos
sys.path.insert(0, str(Path(__file__).parent / 'src'))

try:
    from src.config.user_config import UserConfig
except ImportError:
    print("❌ Error: No se pudo importar el módulo de configuración de usuario.")
    print("   Asegúrate de ejecutar este script desde el directorio raíz del proyecto.")
    sys.exit(1)


def print_banner():
    """Mostrar banner del script."""
    print("\n" + "="*60)
    print("🔧 QAK Browser-Use Events - Configuración de Usuario")
    print("="*60)
    print()


def print_current_config(user_config: UserConfig):
    """Mostrar configuración actual."""
    profile = user_config.get_user_profile()
    
    print("📋 Configuración Actual:")
    print("-" * 30)
    
    if profile:
        print(f"   User ID: {profile.user_id}")
        print(f"   Nombre: {profile.display_name or 'No configurado'}")
        print(f"   Email: {profile.email or 'No configurado'}")
        print(f"   Creado: {profile.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        if profile.last_used:
            print(f"   Último uso: {profile.last_used.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("   ❌ No hay configuración de usuario")
        
        # Mostrar qué user_id se usaría actualmente
        current_user_id = user_config.get_user_id()
        print(f"   🔄 User ID actual (auto-generado): {current_user_id}")
    
    print()


def interactive_setup(user_config: UserConfig):
    """Configuración interactiva."""
    print("🚀 Configuración Interactiva")
    print("-" * 30)
    print()
    
    # Obtener user_id
    while True:
        user_id = input("📝 Ingresa tu User ID (ej: juan_perez, usuario123): ").strip()
        if user_id:
            if len(user_id) < 3:
                print("   ⚠️  El User ID debe tener al menos 3 caracteres.")
                continue
            if ' ' in user_id:
                print("   ⚠️  El User ID no puede contener espacios.")
                continue
            break
        else:
            print("   ⚠️  El User ID es requerido.")
    
    # Obtener nombre para mostrar (opcional)
    display_name = input("👤 Nombre para mostrar (opcional): ").strip() or None
    
    # Obtener email (opcional)
    email = input("📧 Email (opcional): ").strip() or None
    
    # Validar email básico
    if email and '@' not in email:
        print("   ⚠️  Email inválido, se omitirá.")
        email = None
    
    # Confirmar configuración
    print("\n📋 Resumen de Configuración:")
    print(f"   User ID: {user_id}")
    print(f"   Nombre: {display_name or 'No configurado'}")
    print(f"   Email: {email or 'No configurado'}")
    print()
    
    confirm = input("¿Confirmar configuración? (s/N): ").strip().lower()
    if confirm not in ['s', 'si', 'sí', 'y', 'yes']:
        print("❌ Configuración cancelada.")
        return False
    
    # Guardar configuración
    try:
        user_config.set_user_profile(
            user_id=user_id,
            display_name=display_name,
            email=email
        )
        print("\n✅ Configuración guardada exitosamente!")
        return True
    except Exception as e:
        print(f"\n❌ Error guardando configuración: {e}")
        return False


def set_environment_variable(user_id: str):
    """Mostrar instrucciones para configurar variable de entorno."""
    print("\n🔧 Configuración por Variable de Entorno")
    print("-" * 40)
    print()
    print("Para usar variables de entorno, agrega esta línea a tu shell:")
    print()
    
    # Detectar shell
    shell = os.environ.get('SHELL', '/bin/bash')
    if 'zsh' in shell:
        config_file = '~/.zshrc'
    elif 'fish' in shell:
        config_file = '~/.config/fish/config.fish'
        print(f"   set -x QAK_USER_ID '{user_id}'")
        print()
        print(f"O agrega al archivo {config_file}:")
        print(f"   echo \"set -x QAK_USER_ID '{user_id}'\" >> {config_file}")
        return
    else:
        config_file = '~/.bashrc'
    
    print(f"   export QAK_USER_ID='{user_id}'")
    print()
    print(f"O agrega al archivo {config_file}:")
    print(f"   echo \"export QAK_USER_ID='{user_id}'\" >> {config_file}")
    print()
    print("Luego reinicia tu terminal o ejecuta:")
    print(f"   source {config_file}")
    print()


def main():
    parser = argparse.ArgumentParser(
        description="Configurar User ID real para QAK Browser-Use Events",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos:
  python configure_user_id.py                    # Configuración interactiva
  python configure_user_id.py --user-id juan     # Configurar user ID específico
  python configure_user_id.py --email <EMAIL> --name "Juan Pérez"
  python configure_user_id.py --clear            # Limpiar configuración
  python configure_user_id.py --show             # Mostrar configuración actual
  python configure_user_id.py --env-var juan     # Mostrar instrucciones para variable de entorno
"""
    )
    
    parser.add_argument(
        '--user-id', '-u',
        help='User ID a configurar'
    )
    
    parser.add_argument(
        '--name', '-n',
        help='Nombre para mostrar'
    )
    
    parser.add_argument(
        '--email', '-e',
        help='Email del usuario'
    )
    
    parser.add_argument(
        '--clear', '-c',
        action='store_true',
        help='Limpiar configuración actual'
    )
    
    parser.add_argument(
        '--show', '-s',
        action='store_true',
        help='Mostrar configuración actual'
    )
    
    parser.add_argument(
        '--env-var',
        metavar='USER_ID',
        help='Mostrar instrucciones para configurar variable de entorno'
    )
    
    args = parser.parse_args()
    
    print_banner()
    
    # Inicializar configuración
    user_config = UserConfig()
    
    # Mostrar configuración actual
    if args.show:
        print_current_config(user_config)
        return
    
    # Limpiar configuración
    if args.clear:
        try:
            user_config.clear_config()
            print("✅ Configuración limpiada exitosamente.")
            print("   Se usará auto-generación de User ID en próximas ejecuciones.")
        except Exception as e:
            print(f"❌ Error limpiando configuración: {e}")
        return
    
    # Mostrar instrucciones de variable de entorno
    if args.env_var:
        set_environment_variable(args.env_var)
        return
    
    # Mostrar configuración actual primero
    print_current_config(user_config)
    
    # Configuración por argumentos
    if args.user_id:
        try:
            user_config.set_user_profile(
                user_id=args.user_id,
                display_name=args.name,
                email=args.email
            )
            print(f"✅ User ID configurado: {args.user_id}")
            if args.name:
                print(f"   Nombre: {args.name}")
            if args.email:
                print(f"   Email: {args.email}")
        except Exception as e:
            print(f"❌ Error configurando User ID: {e}")
        return
    
    # Configuración interactiva
    if interactive_setup(user_config):
        print()
        print("🎉 ¡Configuración completada!")
        print()
        print("📝 Próximos pasos:")
        print("   1. Los nuevos eventos de browser-use usarán tu User ID real")
        print("   2. Puedes verificar en el dashboard: http://localhost:9001/browser-use-events")
        print("   3. Para cambiar la configuración, ejecuta este script nuevamente")
        print()
        print("💡 Tip: También puedes usar la variable de entorno QAK_USER_ID")
        print("   para sobrescribir temporalmente la configuración.")
    
    print()


if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Configuración cancelada por el usuario.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1)