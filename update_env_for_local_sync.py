#!/usr/bin/env python3
"""
Script para actualizar archivos .env existentes con configuración de browser-use local sync.

Este script actualiza automáticamente tu archivo .env para habilitar
la sincronización local de browser-use sin perder tu configuración existente.
"""

import os
import re
from pathlib import Path

def update_env_file(env_path: str, backup: bool = True) -> bool:
    """
    Actualiza un archivo .env con configuraciones de browser-use local sync.
    
    Args:
        env_path: Ruta al archivo .env
        backup: Si crear un backup del archivo original
    
    Returns:
        True si se actualizó correctamente, False si hubo errores
    """
    
    env_file = Path(env_path)
    
    if not env_file.exists():
        print(f"❌ Archivo no encontrado: {env_path}")
        return False
    
    # Leer contenido actual
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ Error leyendo archivo: {e}")
        return False
    
    # Crear backup si se solicita
    if backup:
        backup_path = f"{env_path}.backup"
        try:
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📋 Backup creado: {backup_path}")
        except Exception as e:
            print(f"⚠️  No se pudo crear backup: {e}")
    
    # Configuraciones de browser-use local sync
    browser_use_config = """
# =============================================================================
# BROWSER-USE CLOUD SYNC - CONFIGURACIÓN LOCAL
# =============================================================================
# Configuración para enviar eventos de browser-use a tu API local
# en lugar de a los servidores externos de la librería
# 🚀 BENEFICIOS: Privacidad total, control completo, sin dependencias externas

# Habilitar sincronización en la nube (con tu API local)
BROWSER_USE_CLOUD_SYNC=true

# URL de tu API local para recibir eventos de browser-use
BROWSER_USE_CLOUD_API_URL=http://localhost:8000

# URL de la interfaz web para monitorear eventos (opcional)
BROWSER_USE_CLOUD_UI_URL=http://localhost:8000/browser-use-ui
"""
    
    # Verificar si ya existe configuración de browser-use cloud sync
    if 'BROWSER_USE_CLOUD_SYNC' in content:
        print("ℹ️  Configuración de browser-use cloud sync ya existe")
        
        # Actualizar valores existentes
        patterns = {
            r'BROWSER_USE_CLOUD_SYNC=.*': 'BROWSER_USE_CLOUD_SYNC=true',
            r'BROWSER_USE_CLOUD_API_URL=.*': 'BROWSER_USE_CLOUD_API_URL=http://localhost:8000',
            r'BROWSER_USE_CLOUD_UI_URL=.*': 'BROWSER_USE_CLOUD_UI_URL=http://localhost:8000/browser-use-ui'
        }
        
        updated = False
        for pattern, replacement in patterns.items():
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                updated = True
        
        if updated:
            print("🔄 Actualizando configuración existente...")
        else:
            print("✅ Configuración ya está correcta")
            return True
    
    else:
        print("➕ Agregando nueva configuración de browser-use cloud sync...")
        
        # Buscar dónde insertar la configuración
        # Intentar insertar después de la sección de browser-use existente
        browser_use_section = re.search(
            r'(# =============================================================================\s*\n# CONFIGURACIONES GENERALES BROWSER-USE\s*\n# =============================================================================.*?)\n\n',
            content,
            re.DOTALL
        )
        
        if browser_use_section:
            # Insertar después de la sección de browser-use
            insert_pos = browser_use_section.end()
            content = content[:insert_pos] + browser_use_config + "\n" + content[insert_pos:]
        else:
            # Si no hay sección de browser-use, agregar al final
            content = content.rstrip() + "\n\n" + browser_use_config + "\n"
    
    # Escribir archivo actualizado
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Archivo actualizado: {env_path}")
        return True
    except Exception as e:
        print(f"❌ Error escribiendo archivo: {e}")
        return False

def verify_configuration() -> bool:
    """
    Verifica que la configuración esté correcta.
    
    Returns:
        True si la configuración es válida
    """
    
    print("\n🔍 Verificando configuración...")
    
    # Verificar variables de entorno
    required_vars = {
        'BROWSER_USE_CLOUD_SYNC': 'true',
        'BROWSER_USE_CLOUD_API_URL': 'http://localhost:8000',
        'BROWSER_USE_CLOUD_UI_URL': 'http://localhost:8000/browser-use-ui'
    }
    
    # Cargar variables desde .env si existe
    env_file = Path('.env')
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                env_content = f.read()
            
            for var, expected in required_vars.items():
                match = re.search(f'^{var}=(.*)$', env_content, re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    if value == expected:
                        print(f"✅ {var}: {value}")
                    else:
                        print(f"⚠️  {var}: {value} (esperado: {expected})")
                else:
                    print(f"❌ {var}: No encontrado")
                    return False
        except Exception as e:
            print(f"❌ Error leyendo .env: {e}")
            return False
    else:
        print("❌ Archivo .env no encontrado")
        return False
    
    return True

def show_next_steps():
    """
    Muestra los siguientes pasos para el usuario.
    """
    
    print("\n🎯 Siguientes pasos:")
    print("=" * 50)
    print("1. Inicia tu API local:")
    print("   python app.py")
    print("\n2. Prueba la configuración:")
    print("   python example_browser_use_local_sync.py")
    print("\n3. Monitorea eventos en:")
    print("   http://localhost:8000/docs")
    print("   http://localhost:8000/api/v1/events")
    print("\n4. Usa browser-use normalmente:")
    print("   Los eventos se enviarán automáticamente a tu API local")

def main():
    """
    Función principal.
    """
    
    print("🔧 Actualizador de configuración browser-use local sync")
    print("=" * 60)
    
    # Archivos a actualizar
    files_to_update = ['.env']
    
    # Verificar si .env.example también debe actualizarse
    if Path('.env.example').exists():
        response = input("\n¿Actualizar también .env.example? (y/n): ").lower().strip()
        if response in ['y', 'yes', 'sí', 's']:
            files_to_update.append('.env.example')
    
    success_count = 0
    
    # Actualizar archivos
    for file_path in files_to_update:
        print(f"\n📝 Actualizando {file_path}...")
        if update_env_file(file_path):
            success_count += 1
        else:
            print(f"❌ Error actualizando {file_path}")
    
    # Verificar configuración
    if success_count > 0:
        if verify_configuration():
            print("\n🎉 ¡Configuración completada exitosamente!")
            show_next_steps()
        else:
            print("\n⚠️  Configuración actualizada pero hay problemas")
            print("   Revisa manualmente los archivos .env")
    else:
        print("\n❌ No se pudo actualizar ningún archivo")
        print("   Verifica los permisos y rutas de archivos")

if __name__ == "__main__":
    main()