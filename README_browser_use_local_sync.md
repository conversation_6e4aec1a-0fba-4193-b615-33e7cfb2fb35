# Browser-Use con Sincronización Local

Esta configuración te permite usar `browser-use` enviando todos los eventos de telemetría y sincronización a tu propia API local en lugar de a los servidores externos de la librería.

## 🎯 ¿Qué se logra?

- **Privacidad**: Todos los datos se quedan en tu servidor local
- **Control**: Puedes ver, analizar y procesar todos los eventos
- **Personalización**: Puedes modificar cómo se almacenan y procesan los datos
- **Sin dependencias externas**: No necesitas conectarte a servicios de terceros

## 📋 Archivos Creados

### 1. `src/api/browser_use_sync_routes.py`
- **Propósito**: Endpoints de API que replican la funcionalidad de sincronización de browser-use
- **Funcionalidad**:
  - Recibe eventos de sesiones, tareas, pasos y archivos
  - Almacena eventos en memoria (puedes modificar para usar base de datos)
  - Proporciona endpoints para consultar eventos
  - Incluye estadísticas y limpieza de datos

### 2. `configure_browser_use_local.py`
- **Propósito**: Script para configurar browser-use para usar tu API local
- **Funcionalidad**:
  - Configura variables de entorno
  - Crea/actualiza archivo `.env`
  - Prueba la configuración
  - Incluye ejemplo de uso

### 3. `example_browser_use_local_sync.py`
- **Propósito**: Ejemplo completo de uso de browser-use con sincronización local
- **Funcionalidad**:
  - Prueba la conexión con tu API
  - Ejecuta una tarea de ejemplo
  - Muestra eventos capturados
  - Proporciona instrucciones de uso

## 🚀 Configuración Paso a Paso

### Paso 1: Configurar Browser-Use

```bash
# Ejecutar el script de configuración
python configure_browser_use_local.py
```

Esto configurará las siguientes variables de entorno:
- `BROWSER_USE_CLOUD_API_URL=http://localhost:8000`
- `BROWSER_USE_CLOUD_SYNC=true`
- `BROWSER_USE_CLOUD_UI_URL=http://localhost:8000/browser-use-ui`

### Paso 2: Configurar API Key de OpenRouter

```bash
# Configura tu API key de OpenRouter
export OPENROUTER_API_KEY="tu-api-key-aqui"

# O agrégala al archivo .env
echo "OPENROUTER_API_KEY=tu-api-key-aqui" >> .env
```

### Paso 3: Iniciar tu API Local

```bash
# Desde el directorio raíz del proyecto
python app.py
```

Tu API estará disponible en `http://localhost:8000`

### Paso 4: Probar la Configuración

```bash
# Ejecutar el ejemplo
python example_browser_use_local_sync.py
```

## 📊 Endpoints de la API

Tu API local ahora incluye estos endpoints para browser-use:

### Eventos
- `POST /api/v1/events` - Recibir lotes de eventos
- `GET /api/v1/events` - Obtener todos los eventos
- `DELETE /api/v1/events` - Limpiar todos los eventos

### Consultas Específicas
- `GET /api/v1/sessions` - Obtener sesiones de navegador
- `GET /api/v1/tasks` - Obtener tareas de agentes
- `GET /api/v1/steps` - Obtener pasos de ejecución
- `GET /api/v1/stats` - Obtener estadísticas

### Documentación
- `GET /docs` - Documentación interactiva de la API

## 🔍 Tipos de Eventos Capturados

### 1. Sesiones de Navegador (`CreateAgentSessionEvent`)
- ID de sesión y usuario
- Configuración del navegador (viewport, user agent, etc.)
- URLs visitadas
- Cookies y configuración de seguridad

### 2. Tareas de Agente (`CreateAgentTaskEvent`)
- Descripción de la tarea
- Modelo LLM utilizado
- Tiempos de inicio y fin
- Estado final y feedback del usuario

### 3. Pasos de Ejecución (`CreateAgentStepEvent`)
- Acciones realizadas
- Screenshots (en base64)
- Evaluaciones y objetivos
- URLs visitadas

### 4. Archivos de Salida (`CreateAgentOutputFileEvent`)
- Archivos generados por el agente
- Contenido en base64
- Metadatos del archivo

### 5. Actualizaciones de Tareas (`UpdateAgentTaskEvent`)
- Cambios en el estado de las tareas
- Actualizaciones de progreso

## 💡 Personalización

### Cambiar Almacenamiento

Por defecto, los eventos se almacenan en memoria. Para usar una base de datos:

```python
# En browser_use_sync_routes.py
# Reemplaza las listas en memoria con modelos de base de datos

from your_database import EventModel

@router.post("/api/v1/events")
async def receive_events(batch: EventBatch):
    for event_data in batch.batch:
        # Guardar en base de datos
        await EventModel.create(**event_data.dict())
    return {"status": "success"}
```

### Agregar Procesamiento Personalizado

```python
# Agregar lógica personalizada para procesar eventos
@router.post("/api/v1/events")
async def receive_events(batch: EventBatch):
    for event_data in batch.batch:
        # Tu lógica personalizada aquí
        if event_data.event_type == "CreateAgentStepEvent":
            await process_step_event(event_data)
        elif event_data.event_type == "CreateAgentTaskEvent":
            await process_task_event(event_data)
    
    return {"status": "success"}
```

### Agregar Autenticación

```python
# Agregar autenticación a los endpoints
from fastapi import Depends, HTTPException
from fastapi.security import HTTPBearer

security = HTTPBearer()

@router.post("/api/v1/events")
async def receive_events(
    batch: EventBatch,
    token: str = Depends(security)
):
    # Validar token
    if not validate_token(token.credentials):
        raise HTTPException(status_code=401, detail="Invalid token")
    
    # Procesar eventos...
```

## 🔧 Solución de Problemas

### Error: "No se pudo conectar a la API local"

1. Verifica que tu API esté ejecutándose:
   ```bash
   curl http://localhost:8000/api/v1/stats
   ```

2. Verifica el puerto en `app.py`

3. Revisa los logs de tu API

### Error: "browser-use no está configurado para API local"

1. Ejecuta el script de configuración:
   ```bash
   python configure_browser_use_local.py
   ```

2. Verifica las variables de entorno:
   ```bash
   echo $BROWSER_USE_CLOUD_API_URL
   ```

### Error: "OPENROUTER_API_KEY no está configurada"

1. Obtén una API key de [OpenRouter](https://openrouter.ai/)

2. Configúrala:
   ```bash
   export OPENROUTER_API_KEY="tu-api-key"
   ```

## 📈 Monitoreo

### Ver Eventos en Tiempo Real

```bash
# Obtener estadísticas
curl http://localhost:8000/api/v1/stats

# Ver últimos eventos
curl http://localhost:8000/api/v1/events

# Ver tareas específicas
curl http://localhost:8000/api/v1/tasks
```

### Usar la Documentación Interactiva

Visita `http://localhost:8000/docs` para una interfaz interactiva donde puedes:
- Ver todos los endpoints
- Probar las APIs
- Ver esquemas de datos
- Ejecutar consultas

## 🎉 ¡Listo!

Ahora puedes usar `browser-use` normalmente y todos los eventos se enviarán a tu API local en lugar de a servidores externos. Tienes control completo sobre tus datos y puedes personalizarlos según tus necesidades.

### Ejemplo de Uso Normal

```python
import asyncio
from browser_use.agent.service import Agent
from browser_use.llm.openrouter.chat import ChatOpenRouter

async def main():
    llm = ChatOpenRouter(
        model="openai/gpt-4o-mini",
        api_key="tu-api-key"
    )
    
    agent = Agent(
        task="Buscar información sobre Python en Wikipedia",
        llm=llm
    )
    
    # Los eventos se enviarán automáticamente a tu API local
    result = await agent.run()
    print(f"Tarea completada: {result}")

if __name__ == "__main__":
    asyncio.run(main())
```

Todos los eventos de esta ejecución aparecerán en `http://localhost:8000/api/v1/events` 🚀