[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "qak"
version = "1.0.0"
description = "QAK Test Execution System"
requires-python = ">=3.11"
dependencies = []

[tool.pylint.main]
# Archivos y directorios a analizar
init-hook = "import sys; sys.path.append('.')"
load-plugins = [
    "pylint.extensions.check_elif",
    "pylint.extensions.bad_builtin",
    "pylint.extensions.docparams",
    "pylint.extensions.for_any_all",
    "pylint.extensions.set_membership",
    "pylint.extensions.code_style",
    "pylint.extensions.overlapping_exceptions",
    "pylint.extensions.typing",
    "pylint.extensions.redefined_variable_type",
    "pylint.extensions.comparison_placement"
]

# Configuración específica para detectar problemas de clases abstractas
[tool.pylint.messages_control]
enable = [
    "abstract-method",
    "abstract-class-instantiated",
    "invalid-metaclass",
    "missing-docstring",
    "unused-import",
    "unused-variable",
    "redefined-outer-name",
    "too-many-arguments",
    "too-many-locals",
    "too-many-branches",
    "too-many-statements",
    "duplicate-code"
]

disable = [
    "raw-checker-failed",
    "bad-inline-option",
    "locally-disabled",
    "file-ignored",
    "suppressed-message",
    "useless-suppression",
    "deprecated-pragma",
    "use-symbolic-message-instead",
    "missing-module-docstring",
    "line-too-long"  # Manejado por black
]

[tool.pylint.reports]
output-format = "colorized"
reports = true
score = true

[tool.pylint.refactoring]
max-nested-blocks = 5
never-returning-functions = ["sys.exit", "argparse.parse_error"]

[tool.pylint.similarities]
min-similarity-lines = 4
ignore-comments = true
ignore-docstrings = true
ignore-imports = true
ignore-signatures = true

[tool.pylint.spelling]
spelling-dict = "en_US"
spelling-ignore-comment-directives = "fmt: off,fmt: on,noqa:,noqa,nosec,isort:skip,mypy:"

[tool.pylint.string]
check-str-concat-over-line-jumps = true

[tool.pylint.typecheck]
contextmanager-decorators = ["contextlib.contextmanager"]
ignore-none = true
ignore-on-opaque-inference = true
mixin-class-rgx = ".*[Mm]ixin"

[tool.pylint.variables]
allow-global-unused-variables = true
callbacks = ["cb_", "_cb"]
dummy-variables-rgx = "_+$|(_[a-zA-Z0-9_]*[a-zA-Z0-9]+?$)|dummy|^ignored_|^unused_"
ignored-argument-names = "_.*|^ignored_|^unused_"
init-import = false
redefining-builtins-modules = ["six.moves", "past.builtins", "future.builtins", "builtins", "io"]

# Configuración de flake8
[tool.flake8]
max-line-length = 120
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "C",  # flake8-comprehensions
    "B",  # flake8-bugbear
    "A",  # flake8-builtins
    "T",  # flake8-print
    "S",  # flake8-bandit
    "N",  # pep8-naming
]
ignore = [
    "E203",  # whitespace before ':' (conflicto con black)
    "E501",  # line too long (manejado por black)
    "W503",  # line break before binary operator
    "S101",  # use of assert
    "S311",  # pseudo-random generators
    "B008",  # function calls in argument defaults
]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "venv",
    "env",
    ".env",
    "build",
    "dist",
    "*.egg-info",
    ".pytest_cache",
    ".coverage",
    "htmlcov",
    "node_modules",
    "static",
    "web",
    "screenshots",
    "redis-data",
    "codegen_sessions"
]
max-complexity = 10
max-cognitive-complexity = 12

# Configuración de mypy para verificación de tipos
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false  # Gradual adoption
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
pretty = true

# Módulos específicos
[[tool.mypy.overrides]]
module = [
    "browser_use.*",
    "playwright.*",
    "celery.*",
    "redis.*",
    "pymongo.*",
    "motor.*",
    "fastapi.*",
    "uvicorn.*",
    "pydantic.*",
    "anthropic.*",
    "openai.*",
    "google.*"
]
ignore_missing_imports = true

# Configuración de black para formateo de código
[tool.black]
line-length = 120
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | node_modules
    | static
    | web
  )/
)
'''

# Configuración de isort para ordenamiento de imports
[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["src", "libs"]
skip_glob = [
    "**/node_modules/**",
    "**/static/**",
    "**/web/**",
    "**/.venv/**",
    "**/venv/**"
]
known_first_party = ["src", "libs", "browser_use"]
known_third_party = [
    "fastapi",
    "pydantic",
    "uvicorn",
    "celery",
    "redis",
    "pymongo",
    "motor",
    "playwright",
    "anthropic",
    "openai",
    "google"
]

# Configuración de bandit para análisis de seguridad
[tool.bandit]
exclude_dirs = [
    "tests",
    "test",
    ".venv",
    "venv",
    "node_modules",
    "static",
    "web"
]
skips = [
    "B101",  # assert_used
    "B601",  # paramiko_calls
    "B602",  # subprocess_popen_with_shell_equals_true
]

# Configuración de coverage para cobertura de tests
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "*/static/*",
    "*/web/*",
    "*/node_modules/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*Protocol",
    "@abstractmethod"
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Configuración de pytest
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
    "src/tests"
]
python_files = [
    "test_*.py",
    "*_test.py"
]
python_classes = [
    "Test*"
]
python_functions = [
    "test_*"
]
markers = [
    "slow: marks tests as slow (deselect with '-m 'not slow'')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "browser: marks tests that require browser automation"
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning"
]