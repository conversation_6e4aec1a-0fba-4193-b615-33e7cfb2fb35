# 🚀 Guía Rápida: User IDs Reales en Browser-Use Events

## ⚡ Configuración en 2 Minutos

### Opción 1: Variable de Entorno (Más Rápido)

```bash
# Configurar tu user ID
export QAK_USER_ID="tu_nombre"

# Verificar
echo "Tu User ID: $QAK_USER_ID"

# Hacer permanente (opcional)
echo 'export QAK_USER_ID="tu_nombre"' >> ~/.bashrc
```

### Opción 2: Script de Configuración (Más Completo)

```bash
# Configuración interactiva
./configure_user_id.py

# O configuración directa
./configure_user_id.py --user-id "tu_nombre" --name "Tu Nombre Completo"
```

## 🧪 Probar la Configuración

### 1. Verificar Configuración

```bash
# Ver configuración actual
./configure_user_id.py --show
```

### 2. Generar Eventos de Prueba

```bash
# Enviar evento de prueba
curl -X POST http://localhost:8000/api/v1/events \
  -H "Content-Type: application/json" \
  -d '[{
    "event_type": "session_started",
    "user_id": "99999999-9999-9999-9999-999999999999",
    "device_id": "test-device",
    "data": {
      "viewport": {"width": 1920, "height": 1080},
      "user_agent": "Test Browser"
    }
  }]'
```

### 3. Ver Resultados

```bash
# Abrir dashboard
open http://localhost:9001/browser-use-events
```

## 🎯 Resultados Esperados

### Antes (TEMP_USER_ID)
```
👤 Usuario: 99999999-9999-9999-9999-999999999999
```

### Después (User ID Real)
```
👤 Usuario: Sistema (tu_nombre)     [Badge azul]
👤 Usuario: <EMAIL>     [Badge índigo]
👤 Usuario: Invitado (a1b2c3d4)     [Badge amarillo]
```

## 🔧 Configuraciones Avanzadas

### Para Equipos de Desarrollo

```bash
# Cada desarrollador configura su ID
export QAK_USER_ID="dev_${USER}"

# O por proyecto
export QAK_USER_ID="proyecto_web_${USER}"
```

### Para CI/CD

```yaml
# GitHub Actions
env:
  QAK_USER_ID: "ci_github_${{ github.actor }}"

# GitLab CI
variables:
  QAK_USER_ID: "ci_gitlab_${GITLAB_USER_LOGIN}"
```

### Para Testing

```bash
# Headers personalizados
curl -X POST http://localhost:8000/api/v1/events \
  -H "X-User-ID: test_user_123" \
  -H "Content-Type: application/json" \
  -d '[{"event_type": "test", "data": {}}]'
```

## 🎨 Tipos de Usuario en la Interfaz

| Configuración | Resultado Visual | Color |
|---------------|------------------|-------|
| `export QAK_USER_ID="juan"` | Sistema (juan) | 🔵 Azul |
| `<EMAIL>` | <EMAIL> | 🟣 Índigo |
| Sin configuración + IP | Invitado (a1b2c3) | 🟡 Amarillo |
| Header `Authorization` | Usuario Auth (xyz) | 🟢 Verde |
| Auto-generado | Usuario (abc123) | 🟣 Púrpura |

## 🔍 Verificación Rápida

### 1. Comprobar que el servidor está corriendo
```bash
curl http://localhost:8000/health || echo "Servidor no disponible"
```

### 2. Comprobar configuración
```bash
./configure_user_id.py --show
```

### 3. Comprobar variable de entorno
```bash
echo "QAK_USER_ID: ${QAK_USER_ID:-'No configurado'}"
```

### 4. Enviar evento de prueba y verificar
```bash
# Enviar evento
curl -s -X POST http://localhost:8000/api/v1/events \
  -H "Content-Type: application/json" \
  -d '[{"event_type": "test", "data": {"test": true}}]' | \
  jq '.real_user_ids_applied'

# Debería mostrar: 1 (si se aplicó user ID real)
```

## 🆘 Solución de Problemas

### Problema: Sigue mostrando TEMP_USER_ID

```bash
# 1. Verificar configuración
./configure_user_id.py --show

# 2. Verificar variable de entorno
echo $QAK_USER_ID

# 3. Reiniciar servidor si es necesario
# 4. Verificar logs
tail -f logs/app.log | grep user_id
```

### Problema: Script no ejecuta

```bash
# Hacer ejecutable
chmod +x configure_user_id.py

# O ejecutar con python
python configure_user_id.py
```

### Problema: Dashboard no muestra cambios

```bash
# Refrescar página
# Verificar consola del navegador
# Limpiar caché del navegador
```

## 📋 Checklist de Configuración

- [ ] ✅ Servidor QAK corriendo en puerto 8000
- [ ] ✅ Dashboard web accesible en puerto 9001
- [ ] ✅ User ID configurado (variable de entorno o archivo)
- [ ] ✅ Script de configuración ejecutable
- [ ] ✅ Evento de prueba enviado exitosamente
- [ ] ✅ Dashboard muestra user ID real (no TEMP_USER_ID)
- [ ] ✅ Colores y formateo correctos en la interfaz

## 🎉 ¡Listo!

Ahora todos tus eventos de browser-use tendrán user IDs reales y significativos. 

**Próximos pasos:**
- Configura tu equipo con IDs únicos
- Explora el dashboard mejorado
- Revisa la documentación completa en `docs/USER_ID_IMPLEMENTATION.md`

---

💡 **Tip:** Usa `./configure_user_id.py --help` para ver todas las opciones disponibles.