#!/usr/bin/env python3
"""
Verificación final del problema de clases abstractas resuelto.
"""

import sys
import os

# Configurar el path
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def main():
    print("🔍 Verificando corrección del error de clases abstractas...\n")
    
    try:
        # Importar las clases de repositorio
        from database.repositories.browser_use_event_repository import (
            BrowserUseSessionRepository,
            BrowserUseTaskRepository,
            BrowserUseStepRepository,
            BrowserUseFileRepository,
            BrowserUseEventRepository
        )
        
        print("✅ Importaciones exitosas")
        
        # Probar instanciación específica que causaba el error
        try:
            session_repo = BrowserUseSessionRepository()
            print("✅ BrowserUseSessionRepository() - INSTANCIACIÓN EXITOSA")
            
            # Verificar que los métodos abstractos estén implementados
            required_methods = ['to_document', 'from_document', 'get_document_id']
            for method in required_methods:
                if hasattr(session_repo, method) and callable(getattr(session_repo, method)):
                    print(f"  ✅ Método {method} implementado")
                else:
                    print(f"  ❌ Método {method} faltante")
                    return False
                    
        except TypeError as e:
            if "abstract" in str(e).lower():
                print(f"❌ ERROR: Aún hay métodos abstractos sin implementar: {e}")
                return False
            else:
                print(f"❌ ERROR: Otro problema de tipo: {e}")
                return False
        
        # Probar las otras clases también
        other_repos = {
            "BrowserUseTaskRepository": BrowserUseTaskRepository,
            "BrowserUseStepRepository": BrowserUseStepRepository,
            "BrowserUseFileRepository": BrowserUseFileRepository,
            "BrowserUseEventRepository": BrowserUseEventRepository
        }
        
        for name, repo_class in other_repos.items():
            try:
                repo = repo_class()
                print(f"✅ {name}() - INSTANCIACIÓN EXITOSA")
            except TypeError as e:
                if "abstract" in str(e).lower():
                    print(f"❌ {name} tiene métodos abstractos sin implementar: {e}")
                    return False
        
        print("\n🎉 VERIFICACIÓN COMPLETA EXITOSA")
        print("🔧 El error original 'Can't instantiate abstract class BrowserUseSessionRepository' ha sido RESUELTO")
        print("✅ Todas las clases de repositorio se instancian correctamente")
        
        return True
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ ESTADO: PROBLEMA RESUELTO")
        sys.exit(0)
    else:
        print("\n❌ ESTADO: PROBLEMA PERSISTE")
        sys.exit(1)