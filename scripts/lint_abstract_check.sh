#!/bin/bash

# Verificación específica para problemas de clases abstractas

echo "🔍 Verificando problemas de clases abstractas..."

# Activar entorno virtual si existe
if [ -d ".venv" ]; then
    source .venv/bin/activate
elif [ -d "venv" ]; then
    source venv/bin/activate
fi

# Verificar problemas específicos de clases abstractas
echo "\n📋 Verificando implementación de clases abstractas con Pylint..."
pylint --errors-only \
       --enable=abstract-method,abstract-class-instantiated,invalid-metaclass \
       --disable=all \
       src/database/repositories/ 2>/dev/null || {
    echo "❌ Se encontraron problemas de clases abstractas"
    echo "💡 Revisa la implementación de métodos abstractos en los repositorios"
    exit 1
}

echo "✅ No se encontraron problemas de clases abstractas"

# Verificar errores críticos de sintaxis
echo "\n📋 Verificando errores críticos con Flake8..."
flake8 --select=E9,F63,F7,F82 src/ 2>/dev/null || {
    echo "❌ Se encontraron errores críticos de sintaxis"
    exit 1
}

echo "✅ No se encontraron errores críticos de sintaxis"

echo "\n🎉 Verificación completada - Código listo para producción"
