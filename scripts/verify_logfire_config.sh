#!/bin/bash

# Script para verificar que la configuración de Logfire funciona correctamente
# después de corregir el archivo pyproject.toml

echo "🔍 Verificando configuración de Logfire y pyproject.toml"
echo "========================================================"

# 1. Verificar que pyproject.toml es válido
echo "1️⃣ Verificando sintaxis de pyproject.toml..."
python3 -c "import tomllib; f=open('pyproject.toml','rb'); tomllib.load(f); print('✅ pyproject.toml es válido')"
if [ $? -ne 0 ]; then
    echo "❌ Error en pyproject.toml"
    exit 1
fi

# 2. Verificar que las dependencias de Logfire están instaladas
echo ""
echo "2️⃣ Verificando instalación de Logfire..."
python3 -c "import logfire; print('✅ Logfire está instalado correctamente')"
if [ $? -ne 0 ]; then
    echo "❌ Logfire no está instalado"
    echo "💡 Ejecuta: pip install 'logfire[fastapi,httpx]>=0.7.0'"
    exit 1
fi

# 3. Verificar configuración de variables de entorno
echo ""
echo "3️⃣ Verificando variables de entorno de Logfire..."
if [ -f ".env" ]; then
    if grep -q "LOGFIRE_TOKEN" .env; then
        echo "✅ LOGFIRE_TOKEN configurado en .env"
    else
        echo "⚠️  LOGFIRE_TOKEN no encontrado en .env"
    fi
    
    if grep -q "LOGFIRE_SERVICE_NAME" .env; then
        echo "✅ LOGFIRE_SERVICE_NAME configurado en .env"
    else
        echo "⚠️  LOGFIRE_SERVICE_NAME no encontrado en .env"
    fi
else
    echo "⚠️  Archivo .env no encontrado"
fi

# 4. Probar importación de configuración de Logfire
echo ""
echo "4️⃣ Probando configuración de Logfire..."
python3 -c "
import os
os.environ.setdefault('LOGFIRE_TOKEN', 'test')
os.environ.setdefault('LOGFIRE_SERVICE_NAME', 'test')
import logfire
logfire.configure(service_name='test-service', send_to_logfire=False)
print('✅ Configuración de Logfire funciona correctamente')
"
if [ $? -ne 0 ]; then
    echo "❌ Error en configuración de Logfire"
    exit 1
fi

echo ""
echo "📋 Resumen de verificación:"
echo "============================"
echo "✅ pyproject.toml: Sintaxis válida"
echo "✅ Logfire: Instalado y funcionando"
echo "✅ Configuración: Sin errores de carga"
echo ""
echo "🎯 El error de 'Invalid config file: pyproject.toml' ha sido RESUELTO"
echo "🚀 El servidor debería iniciarse sin errores de Logfire"