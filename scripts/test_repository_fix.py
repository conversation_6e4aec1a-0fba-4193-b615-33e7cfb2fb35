#!/usr/bin/env python3
"""
Script para verificar que el problema de clases abstractas esté resuelto.
"""

import sys
import os

# Agregar el directorio del proyecto al path
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def test_repository_instantiation():
    """Probar la instanciación de los repositorios."""
    try:
        from database.repositories.browser_use_event_repository import (
            BrowserUseSessionRepository,
            BrowserUseTaskRepository,
            BrowserUseStepRepository,
            BrowserUseFileRepository,
            BrowserUseEventRepository
        )
        
        print("✅ Importaciones exitosas")
        
        # Intentar instanciar cada repositorio
        repositories = {
            "BrowserUseSessionRepository": BrowserUseSessionRepository,
            "BrowserUseTaskRepository": BrowserUseTaskRepository,
            "BrowserUseStepRepository": BrowserUseStepRepository,
            "BrowserUseFileRepository": BrowserUseFileRepository,
            "BrowserUseEventRepository": BrowserUseEventRepository
        }
        
        for name, repo_class in repositories.items():
            try:
                repo = repo_class()
                print(f"✅ {name} instanciado correctamente")
                
                # Verificar que los métodos abstractos estén implementados
                methods = ['to_document', 'from_document', 'get_document_id']
                for method in methods:
                    if hasattr(repo, method) and callable(getattr(repo, method)):
                        print(f"  ✅ Método {method} implementado")
                    else:
                        print(f"  ❌ Método {method} no encontrado")
                        
            except TypeError as e:
                if "abstract" in str(e):
                    print(f"❌ {name} tiene métodos abstractos sin implementar: {e}")
                    return False
                else:
                    print(f"❌ Error al instanciar {name}: {e}")
                    return False
            except Exception as e:
                print(f"⚠️  {name} instanciado pero con advertencia: {e}")
        
        print("\n🎉 Todos los repositorios se instancian correctamente")
        print("🔧 El problema de clases abstractas ha sido resuelto")
        return True
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Verificando corrección del problema de clases abstractas...\n")
    
    success = test_repository_instantiation()
    
    if success:
        print("\n✅ VERIFICACIÓN EXITOSA: El problema ha sido corregido")
        sys.exit(0)
    else:
        print("\n❌ VERIFICACIÓN FALLIDA: El problema persiste")
        sys.exit(1)