{"data_mtime": 1752086943, "dep_lines": [13, 5, 9, 3, 6, 8, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_openai.embeddings.base", "collections.abc", "langchain_core.utils", "__future__", "typing", "openai", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "annotated_types", "httpx", "httpx._client", "httpx._config", "httpx._urls", "langchain_core", "langchain_core.embeddings", "langchain_core.embeddings.embeddings", "langchain_core.utils.utils", "openai._base_client", "openai._client", "openai._resource", "openai._types", "openai.lib", "openai.lib.azure", "openai.resources", "openai.resources.embeddings", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "re"], "hash": "deb9e3130077b95b0b24cb189030427242d6bb99", "id": "langchain_openai.embeddings.azure", "ignore_all": true, "interface_hash": "ad9312d7ad04d742be619fa6c55817644a196cda", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_openai/embeddings/azure.py", "plugin_data": null, "size": 9213, "suppressed": [], "version_id": "1.16.1"}