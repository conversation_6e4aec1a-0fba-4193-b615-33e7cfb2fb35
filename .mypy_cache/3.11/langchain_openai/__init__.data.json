{".class": "MypyFile", "_fullname": "langchain_openai", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AzureChatOpenAI": {".class": "SymbolTableNode", "cross_ref": "langchain_openai.chat_models.azure.AzureChatOpenAI", "kind": "Gdef"}, "AzureOpenAI": {".class": "SymbolTableNode", "cross_ref": "langchain_openai.llms.azure.AzureOpenAI", "kind": "Gdef"}, "AzureOpenAIEmbeddings": {".class": "SymbolTableNode", "cross_ref": "langchain_openai.embeddings.azure.AzureOpenAIEmbeddings", "kind": "Gdef"}, "ChatOpenAI": {".class": "SymbolTableNode", "cross_ref": "langchain_openai.chat_models.base.ChatOpenAI", "kind": "Gdef"}, "OpenAI": {".class": "SymbolTableNode", "cross_ref": "langchain_openai.llms.base.OpenAI", "kind": "Gdef"}, "OpenAIEmbeddings": {".class": "SymbolTableNode", "cross_ref": "langchain_openai.embeddings.base.OpenAIEmbeddings", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_openai.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_openai/__init__.py"}