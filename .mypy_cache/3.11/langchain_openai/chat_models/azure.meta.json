{"data_mtime": 1752086943, "dep_lines": [12, 17, 21, 7, 11, 13, 14, 15, 16, 3, 5, 6, 8, 10, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.language_models.chat_models", "langchain_core.utils.pydantic", "langchain_openai.chat_models.base", "collections.abc", "langchain_core.language_models", "langchain_core.messages", "langchain_core.outputs", "langchain_core.runnables", "langchain_core.utils", "__future__", "logging", "os", "typing", "openai", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "httpx", "httpx._client", "httpx._config", "httpx._urls", "langchain_core", "langchain_core.caches", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.language_models.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.outputs.chat_generation", "langchain_core.outputs.chat_result", "langchain_core.outputs.generation", "langchain_core.prompt_values", "langchain_core.rate_limiters", "langchain_core.runnables.base", "langchain_core.utils.utils", "openai._base_client", "openai._client", "openai._models", "openai._types", "openai.lib", "openai.lib.azure", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "re", "types"], "hash": "edf2086d85ba3e5efab27ee8bfa3183cc1dbaa3b", "id": "langchain_openai.chat_models.azure", "ignore_all": true, "interface_hash": "56a43a37ea4a15a505c16ae481cb7bb3ccfcd2aa", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_openai/chat_models/azure.py", "plugin_data": null, "size": 45644, "suppressed": [], "version_id": "1.16.1"}