{".class": "MypyFile", "_fullname": "langchain_openai.chat_models._compat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AIMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.ai.AIMessage", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_FUNCTION_CALL_IDS_MAP_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langchain_openai.chat_models._compat._FUNCTION_CALL_IDS_MAP_KEY", "name": "_FUNCTION_CALL_IDS_MAP_KEY", "setter_type": null, "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.chat_models._compat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.chat_models._compat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.chat_models._compat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.chat_models._compat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.chat_models._compat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_openai.chat_models._compat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_convert_from_v03_ai_message": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_openai.chat_models._compat._convert_from_v03_ai_message", "name": "_convert_from_v03_ai_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": ["langchain_core.messages.ai.AIMessage"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_from_v03_ai_message", "ret_type": "langchain_core.messages.ai.AIMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_to_v03_ai_message": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["message", "has_reasoning"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_openai.chat_models._compat._convert_to_v03_ai_message", "name": "_convert_to_v03_ai_message", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["message", "has_reasoning"], "arg_types": ["langchain_core.messages.ai.AIMessage", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_to_v03_ai_message", "ret_type": "langchain_core.messages.ai.AIMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_openai/chat_models/_compat.py"}