{"data_mtime": 1752086943, "dep_lines": [35, 41, 67, 72, 74, 87, 89, 91, 95, 100, 105, 109, 115, 13, 30, 36, 40, 47, 73, 80, 81, 88, 90, 102, 3, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 32, 33, 34, 101, 103, 730, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core._api.deprecation", "langchain_core.language_models.chat_models", "langchain_core.messages.ai", "langchain_core.messages.tool", "langchain_core.output_parsers.openai_tools", "langchain_core.runnables.config", "langchain_core.tools.base", "langchain_core.utils.function_calling", "langchain_core.utils.pydantic", "langchain_core.utils.utils", "langchain_openai.chat_models._client_utils", "langchain_openai.chat_models._compat", "openai.types.responses", "collections.abc", "urllib.parse", "langchain_core.callbacks", "langchain_core.language_models", "langchain_core.messages", "langchain_core.output_parsers", "langchain_core.outputs", "langchain_core.runnables", "langchain_core.tools", "langchain_core.utils", "pydantic.v1", "__future__", "base64", "json", "logging", "os", "re", "ssl", "sys", "warnings", "functools", "io", "math", "operator", "typing", "certifi", "openai", "tiktoken", "pydantic", "typing_extensions", "httpx", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "_warnings", "abc", "annotated_types", "certifi.core", "enum", "http", "http.cookiejar", "httpx._auth", "httpx._client", "httpx._config", "httpx._models", "httpx._transports", "httpx._transports.base", "httpx._urls", "langchain_core", "langchain_core._api", "langchain_core.caches", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.language_models.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.outputs.chat_generation", "langchain_core.outputs.chat_result", "langchain_core.outputs.generation", "langchain_core.prompt_values", "langchain_core.rate_limiters", "langchain_core.runnables.base", "openai._base_client", "openai._client", "openai._exceptions", "openai._models", "openai._types", "openai.types", "openai.types.responses.response", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "tiktoken.core", "types"], "hash": "102adf8b24303ed3aeb86894a72fc64614937242", "id": "langchain_openai.chat_models.base", "ignore_all": true, "interface_hash": "02657b81910380d955a9694c7251fc3c9a70af3a", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_openai/chat_models/base.py", "plugin_data": null, "size": 156880, "suppressed": [], "version_id": "1.16.1"}