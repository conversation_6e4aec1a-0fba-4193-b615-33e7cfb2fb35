{"data_mtime": 1752086943, "dep_lines": [14, 17, 5, 10, 15, 16, 1, 3, 4, 6, 8, 9, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.language_models.llms", "langchain_core.utils.utils", "collections.abc", "langchain_core.callbacks", "langchain_core.outputs", "langchain_core.utils", "__future__", "logging", "sys", "typing", "openai", "tiktoken", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "httpx", "httpx._client", "httpx._config", "httpx._urls", "langchain_core", "langchain_core.caches", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.language_models", "langchain_core.language_models.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.outputs.chat_generation", "langchain_core.outputs.generation", "langchain_core.outputs.llm_result", "langchain_core.runnables", "langchain_core.runnables.base", "openai._base_client", "openai._client", "openai._resource", "openai._types", "openai.resources", "openai.resources.completions", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "re", "types"], "hash": "0808647216501d4b478beec4cdacdaad08193fd9", "id": "langchain_openai.llms.base", "ignore_all": true, "interface_hash": "fac3f509ea60af2cfb17019ca1dae6498be68297", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_openai/llms/base.py", "plugin_data": null, "size": 26855, "suppressed": [], "version_id": "1.16.1"}