{"data_mtime": 1752086944, "dep_lines": [32, 31, 32, 33, 18, 28, 31, 16, 19, 20, 21, 22, 23, 24, 26, 28, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 5, 10, 20, 5, 10, 10, 10, 10, 10, 10, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.generativeai.types.permission_types", "google.generativeai.protos", "google.generativeai.types", "google.generativeai.string_utils", "collections.abc", "urllib.request", "google.generativeai", "__future__", "csv", "dataclasses", "datetime", "json", "pathlib", "re", "typing", "urllib", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "google.ai", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.model", "google.ai.generativelanguage_v1beta.types.tuned_model", "os"], "hash": "48a90948926efcaf31c2439a3dd74a8c79021033", "id": "google.generativeai.types.model_types", "ignore_all": true, "interface_hash": "2750a488f048087b40c2fe188be0797063f30212", "mtime": 1752018669, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/google/generativeai/types/model_types.py", "plugin_data": null, "size": 12760, "suppressed": [], "version_id": "1.16.1"}