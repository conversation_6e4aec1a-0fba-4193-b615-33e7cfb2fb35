{"data_mtime": 1752086944, "dep_lines": [20, 21, 20, 15, 16, 18, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 5, 5, 5, 30, 30], "dependencies": ["google.generativeai.protos", "google.generativeai.string_utils", "google.generativeai", "__future__", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "6b177c948c6e093ec825b1227606531e2dc3c00d", "id": "google.generativeai.types.citation_types", "ignore_all": true, "interface_hash": "a396d1ec0623787739a3965f453c424b7fc3f9fa", "mtime": 1752018669, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/google/generativeai/types/citation_types.py", "plugin_data": null, "size": 1229, "suppressed": [], "version_id": "1.16.1"}