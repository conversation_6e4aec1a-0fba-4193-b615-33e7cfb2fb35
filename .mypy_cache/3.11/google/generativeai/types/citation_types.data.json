{".class": "MypyFile", "_fullname": "google.generativeai.types.citation_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CitationMetadataDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.citation_types.CitationMetadataDict", "name": "CitationMetadataDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.citation_types.CitationMetadataDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.citation_types", "mro": ["google.generativeai.types.citation_types.CitationMetadataDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["citation_sources", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.citation_types.CitationSourceDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], ["__doc__", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": ["__doc__", "citation_sources"]}}}, "CitationSourceDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.citation_types.CitationSourceDict", "name": "CitationSourceDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.citation_types.CitationSourceDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.citation_types", "mro": ["google.generativeai.types.citation_types.CitationSourceDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["start_index", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["end_index", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["uri", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["license", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["__doc__", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": ["__doc__", "end_index", "license", "start_index", "uri"]}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.citation_types.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.citation_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.citation_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.citation_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.citation_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.citation_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.citation_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef", "module_public": false}, "string_utils": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.string_utils", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/google/generativeai/types/citation_types.py"}