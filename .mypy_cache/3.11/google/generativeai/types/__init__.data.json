{".class": "MypyFile", "_fullname": "google.generativeai.types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyModelNameOptions": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.AnyModelNameOptions", "kind": "Gdef"}, "AsyncGenerateContentResponse": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.AsyncGenerateContentResponse", "kind": "Gdef"}, "BaseModelNameOptions": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.BaseModelNameOptions", "kind": "Gdef"}, "BlobDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.BlobDict", "kind": "Gdef"}, "BlobType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.BlobType", "kind": "Gdef"}, "BlockedPromptException": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.BlockedPromptException", "kind": "Gdef"}, "BlockedReason": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.BlockedReason", "kind": "Gdef"}, "BrokenResponseError": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.BrokenResponseError", "kind": "Gdef"}, "CallableFunctionDeclaration": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.CallableFunctionDeclaration", "kind": "Gdef"}, "CitationMetadataDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.citation_types.CitationMetadataDict", "kind": "Gdef"}, "CitationSourceDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.citation_types.CitationSourceDict", "kind": "Gdef"}, "ContentDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.ContentDict", "kind": "Gdef"}, "ContentFilterDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.ContentFilterDict", "kind": "Gdef"}, "ContentType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.ContentType", "kind": "Gdef"}, "ContentsType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.ContentsType", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.file_types.File", "kind": "Gdef"}, "FileDataDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.file_types.FileDataDict", "kind": "Gdef"}, "FileDataType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.file_types.FileDataType", "kind": "Gdef"}, "FunctionDeclaration": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.FunctionDeclaration", "kind": "Gdef"}, "FunctionDeclarationType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.FunctionDeclarationType", "kind": "Gdef"}, "FunctionLibrary": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.FunctionLibrary", "kind": "Gdef"}, "FunctionLibraryType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.FunctionLibraryType", "kind": "Gdef"}, "GenerateContentResponse": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.GenerateContentResponse", "kind": "Gdef"}, "GenerationConfig": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.GenerationConfig", "kind": "Gdef"}, "GenerationConfigDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.GenerationConfigDict", "kind": "Gdef"}, "GenerationConfigType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.GenerationConfigType", "kind": "Gdef"}, "HarmBlockThreshold": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.HarmBlockThreshold", "kind": "Gdef"}, "HarmCategory": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.HarmCategory", "kind": "Gdef"}, "HarmProbability": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.HarmProbability", "kind": "Gdef"}, "IncompleteIterationError": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.IncompleteIterationError", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.Model", "kind": "Gdef"}, "ModelNameOptions": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.ModelNameOptions", "kind": "Gdef"}, "ModelsIterable": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.ModelsIterable", "kind": "Gdef"}, "PartDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.PartDict", "kind": "Gdef"}, "PartType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.PartType", "kind": "Gdef"}, "Permission": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.permission_types.Permission", "kind": "Gdef"}, "Permissions": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.permission_types.Permissions", "kind": "Gdef"}, "RequestOptions": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.helper_types.RequestOptions", "kind": "Gdef"}, "RequestOptionsType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.helper_types.RequestOptionsType", "kind": "Gdef"}, "SafetyFeedbackDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.SafetyFeedbackDict", "kind": "Gdef"}, "SafetyRatingDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.SafetyRatingDict", "kind": "Gdef"}, "SafetySettingDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.safety_types.SafetySettingDict", "kind": "Gdef"}, "Status": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.file_types.Status", "kind": "Gdef"}, "StopCandidateException": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.generation_types.StopCandidateException", "kind": "Gdef"}, "StrictContentType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.StrictContentType", "kind": "Gdef"}, "Tool": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.Tool", "kind": "Gdef"}, "ToolDict": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.ToolDict", "kind": "Gdef"}, "ToolsType": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.content_types.ToolsType", "kind": "Gdef"}, "TunedModel": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.TunedModel", "kind": "Gdef"}, "TunedModelNameOptions": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.TunedModelNameOptions", "kind": "Gdef"}, "TunedModelState": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types.TunedModelState", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "get_default_file_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_file_client", "kind": "Gdef"}, "pprint": {".class": "SymbolTableNode", "cross_ref": "pprint", "kind": "Gdef"}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef"}, "to_file_data": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.file_types.to_file_data", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/google/generativeai/types/__init__.py"}