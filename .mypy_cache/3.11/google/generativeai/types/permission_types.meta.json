{"data_mtime": 1752086944, "dep_lines": [21, 22, 26, 28, 29, 21, 22, 15, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24], "dep_prios": [10, 10, 5, 5, 10, 20, 20, 5, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["google.ai.generativelanguage", "google.generativeai.protos", "google.generativeai.client", "google.generativeai.utils", "google.generativeai.string_utils", "google.ai", "google.generativeai", "__future__", "dataclasses", "typing", "re", "google", "builtins", "_frozen_importlib", "abc", "enum", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.permission_service", "google.ai.generativelanguage_v1beta.services.permission_service.async_client", "google.ai.generativelanguage_v1beta.services.permission_service.client", "google.ai.generativelanguage_v1beta.services.permission_service.pagers", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.permission", "google.ai.generativelanguage_v1beta.types.permission_service", "google.api_core", "google.api_core.gapic_v1", "google.api_core.gapic_v1.method", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "types", "typing_extensions"], "hash": "0cba7ff2e2650c8eef49fde2f56f333fc3373370", "id": "google.generativeai.types.permission_types", "ignore_all": true, "interface_hash": "4b024b19dc18e34d1f1f341bfa22c34124958d8f", "mtime": 1752018669, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/google/generativeai/types/permission_types.py", "plugin_data": null, "size": 16549, "suppressed": ["google.protobuf"], "version_id": "1.16.1"}