{".class": "MypyFile", "_fullname": "google.generativeai.types.file_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.file_types.File", "name": "File", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.file_types.File", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.file_types", "mro": ["google.generativeai.types.file_types.File", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proto"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "proto"], "arg_types": ["google.generativeai.types.file_types.File", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.file.File", "google.generativeai.types.file_types.File", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.file_types.File.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.file_types.File.__str__", "name": "__str__", "type": null}}, "_proto": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.file_types.File._proto", "name": "_proto", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.file.File"}}, "create_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.create_time", "name": "create_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_time of File", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.create_time", "name": "create_time", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_time of File", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.file_types.File.delete", "name": "delete", "type": null}}, "display_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.display_name", "name": "display_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "display_name of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.display_name", "name": "display_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "display_name of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "error of File", "ret_type": {".class": "AnyType", "missing_import_name": "google.generativeai.types.file_types.Status", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.error", "name": "error", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "error of File", "ret_type": {".class": "AnyType", "missing_import_name": "google.generativeai.types.file_types.Status", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "expiration_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.expiration_time", "name": "expiration_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "expiration_time of File", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.expiration_time", "name": "expiration_time", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "expiration_time of File", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mime_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.mime_type", "name": "mime_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mime_type of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.mime_type", "name": "mime_type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mime_type of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sha256_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.sha256_hash", "name": "sha256_hash", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sha256_hash of File", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.sha256_hash", "name": "sha256_hash", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sha256_hash of File", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "size_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.size_bytes", "name": "size_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "size_bytes of File", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.size_bytes", "name": "size_bytes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "size_bytes of File", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of File", "ret_type": "google.ai.generativelanguage_v1beta.types.file.File.State", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.state", "name": "state", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of File", "ret_type": "google.ai.generativelanguage_v1beta.types.file.File.State", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_dict of File", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.to_proto", "name": "to_proto", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_proto of File", "ret_type": "google.ai.generativelanguage_v1beta.types.file.File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.update_time", "name": "update_time", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_time of File", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.update_time", "name": "update_time", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_time of File", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.uri", "name": "uri", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uri of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.uri", "name": "uri", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uri of File", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "video_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.file_types.File.video_metadata", "name": "video_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "video_metadata of File", "ret_type": "google.ai.generativelanguage_v1beta.types.file.VideoMetadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.File.video_metadata", "name": "video_metadata", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.file_types.File"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "video_metadata of File", "ret_type": "google.ai.generativelanguage_v1beta.types.file.VideoMetadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.file_types.File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.file_types.File", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileDataDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.file_types.FileDataDict", "name": "FileDataDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.file_types.FileDataDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.file_types", "mro": ["google.generativeai.types.file_types.FileDataDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mime_type", "builtins.str"], ["file_uri", "builtins.str"]], "readonly_keys": [], "required_keys": ["file_uri", "mime_type"]}}}, "FileDataType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.file_types.FileDataType", "line": 117, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.file_types.FileDataDict"}, "google.ai.generativelanguage_v1beta.types.content.FileData", "google.ai.generativelanguage_v1beta.types.file.File", "google.generativeai.types.file_types.File"], "uses_pep604_syntax": false}}}, "Status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.file_types.Status", "name": "Status", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.generativeai.types.file_types.Status", "source_any": null, "type_of_any": 3}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.file_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.file_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.file_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.file_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.file_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.file_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "get_default_file_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_file_client", "kind": "Gdef"}, "pprint": {".class": "SymbolTableNode", "cross_ref": "pprint", "kind": "Gdef"}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef"}, "to_file_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["file_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.file_types.to_file_data", "name": "to_file_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["file_data"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.file_types.FileDataType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_file_data", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/google/generativeai/types/file_types.py"}