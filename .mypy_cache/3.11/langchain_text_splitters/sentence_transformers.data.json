{".class": "MypyFile", "_fullname": "langchain_text_splitters.sentence_transformers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SentenceTransformersTokenTextSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_text_splitters.base.TextSplitter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "name": "SentenceTransformersTokenTextSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_text_splitters.sentence_transformers", "mro": ["langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "langchain_text_splitters.base.TextSplitter", "langchain_core.documents.transformers.BaseDocumentTransformer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "chunk_overlap", "model_name", "tokens_per_chunk", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "chunk_overlap", "model_name", "tokens_per_chunk", "kwargs"], "arg_types": ["langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SentenceTransformersTokenTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter._encode", "name": "_encode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_encode of SentenceTransformersTokenTextSplitter", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialize_chunk_configuration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "tokens_per_chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter._initialize_chunk_configuration", "name": "_initialize_chunk_configuration", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "tokens_per_chunk"], "arg_types": ["langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_initialize_chunk_configuration of SentenceTransformersTokenTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_max_length_equal_32_bit_integer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter._max_length_equal_32_bit_integer", "name": "_max_length_equal_32_bit_integer", "setter_type": null, "type": "builtins.int"}}, "_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter._model", "name": "_model", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "count_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.count_tokens", "name": "count_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "text"], "arg_types": ["langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_tokens of SentenceTransformersTokenTextSplitter", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maximum_tokens_per_chunk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.maximum_tokens_per_chunk", "name": "maximum_tokens_per_chunk", "setter_type": null, "type": "builtins.int"}}, "model_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.model_name", "name": "model_name", "setter_type": null, "type": "builtins.str"}}, "split_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.split_text", "name": "split_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "split_text of SentenceTransformersTokenTextSplitter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.tokenizer", "name": "tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "tokens_per_chunk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.tokens_per_chunk", "name": "tokens_per_chunk", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_text_splitters.sentence_transformers.SentenceTransformersTokenTextSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextSplitter": {".class": "SymbolTableNode", "cross_ref": "langchain_text_splitters.base.TextSplitter", "kind": "Gdef"}, "Tokenizer": {".class": "SymbolTableNode", "cross_ref": "langchain_text_splitters.base.Tokenizer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.sentence_transformers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.sentence_transformers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.sentence_transformers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.sentence_transformers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.sentence_transformers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.sentence_transformers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "split_text_on_tokens": {".class": "SymbolTableNode", "cross_ref": "langchain_text_splitters.base.split_text_on_tokens", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_text_splitters/sentence_transformers.py"}