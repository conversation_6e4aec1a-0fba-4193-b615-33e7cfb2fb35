{"data_mtime": 1752086943, "dep_lines": [5, 22, 1, 3, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_text_splitters.base", "sentence_transformers.SentenceTransformer", "__future__", "typing", "sentence_transformers", "builtins", "_frozen_importlib", "abc", "enum", "huggingface_hub", "huggingface_hub.repocard_data", "langchain_core", "langchain_core.documents", "langchain_core.documents.transformers", "sentence_transformers.fit_mixin", "sentence_transformers.model_card", "sentence_transformers.peft_mixin", "sentence_transformers.similarity_functions", "torch", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module"], "hash": "f7589a6248dcafe08b6b91ef1158810bb5b7e39e", "id": "langchain_text_splitters.sentence_transformers", "ignore_all": true, "interface_hash": "9dc10b730b6e084157db22979f69423b07876198", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_text_splitters/sentence_transformers.py", "plugin_data": null, "size": 3787, "suppressed": [], "version_id": "1.16.1"}