{"data_mtime": 1752086943, "dep_lines": [6, 8, 9, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.documents", "langchain_text_splitters.base", "langchain_text_splitters.character", "__future__", "re", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "langchain_core", "langchain_core.documents.base", "langchain_core.documents.transformers", "langchain_core.load", "langchain_core.load.serializable", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "70897df381b822ff2692742db3622e2aabfd1b05", "id": "langchain_text_splitters.markdown", "ignore_all": true, "interface_hash": "501cf045e075527b7127d7516a8beced0763e642", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_text_splitters/markdown.py", "plugin_data": null, "size": 17126, "suppressed": [], "version_id": "1.16.1"}