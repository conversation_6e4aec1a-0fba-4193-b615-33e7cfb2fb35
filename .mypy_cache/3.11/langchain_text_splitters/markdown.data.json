{".class": "MypyFile", "_fullname": "langchain_text_splitters.markdown", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "langchain_core.documents.base.Document", "kind": "Gdef"}, "ExperimentalMarkdownSyntaxTextSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "name": "ExperimentalMarkdownSyntaxTextSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_text_splitters.markdown", "mro": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_HEADER_KEYS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.DEFAULT_HEADER_KEYS", "name": "DEFAULT_HEADER_KEYS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "headers_to_split_on", "return_each_line", "strip_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "headers_to_split_on", "return_each_line", "strip_headers"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_complete_chunk_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter._complete_chunk_doc", "name": "_complete_chunk_doc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_complete_chunk_doc of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter._match_code", "name": "_match_code", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_match_code of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter._match_header", "name": "_match_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_match_header of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_horz": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter._match_horz", "name": "_match_horz", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "line"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_match_horz of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_code_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "current_line", "raw_lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter._resolve_code_chunk", "name": "_resolve_code_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "current_line", "raw_lines"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_resolve_code_chunk of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_header_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "header_depth", "header_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter._resolve_header_stack", "name": "_resolve_header_stack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "header_depth", "header_text"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.int", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_resolve_header_stack of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.chunks", "name": "chunks", "setter_type": null, "type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "current_chunk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.current_chunk", "name": "current_chunk", "setter_type": null, "type": "langchain_core.documents.base.Document"}}, "current_header_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.current_header_stack", "name": "current_header_stack", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "return_each_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.return_each_line", "name": "return_each_line", "setter_type": null, "type": "builtins.bool"}}, "split_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.split_text", "name": "split_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "split_text of ExperimentalMarkdownSyntaxTextSplitter", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "splittable_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.splittable_headers", "name": "splittable_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "strip_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.strip_headers", "name": "strip_headers", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_text_splitters.markdown.ExperimentalMarkdownSyntaxTextSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_text_splitters.markdown.HeaderType", "name": "HeaderType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_text_splitters.markdown.HeaderType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_text_splitters.markdown", "mro": ["langchain_text_splitters.markdown.HeaderType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["level", "builtins.int"], ["name", "builtins.str"], ["data", "builtins.str"]], "readonly_keys": [], "required_keys": ["data", "level", "name"]}}}, "Language": {".class": "SymbolTableNode", "cross_ref": "langchain_text_splitters.base.Language", "kind": "Gdef"}, "LineType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_text_splitters.markdown.LineType", "name": "LineType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_text_splitters.markdown.LineType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_text_splitters.markdown", "mro": ["langchain_text_splitters.markdown.LineType", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["metadata", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], ["content", "builtins.str"]], "readonly_keys": [], "required_keys": ["content", "metadata"]}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MarkdownHeaderTextSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", "name": "MarkdownHeaderTextSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_text_splitters.markdown", "mro": ["langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "headers_to_split_on", "return_each_line", "strip_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "headers_to_split_on", "return_each_line", "strip_headers"], "arg_types": ["langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MarkdownHeaderTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aggregate_lines_to_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.aggregate_lines_to_chunks", "name": "aggregate_lines_to_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_text_splitters.markdown.LineType"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate_lines_to_chunks of MarkdownHeaderTextSplitter", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "headers_to_split_on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.headers_to_split_on", "name": "headers_to_split_on", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "return_each_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.return_each_line", "name": "return_each_line", "setter_type": null, "type": "builtins.bool"}}, "split_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.split_text", "name": "split_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "split_text of MarkdownHeaderTextSplitter", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strip_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.strip_headers", "name": "strip_headers", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_text_splitters.markdown.MarkdownHeaderTextSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkdownTextSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_text_splitters.character.RecursiveCharacterTextSplitter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_text_splitters.markdown.MarkdownTextSplitter", "name": "MarkdownTextSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_text_splitters.markdown.MarkdownTextSplitter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_text_splitters.markdown", "mro": ["langchain_text_splitters.markdown.MarkdownTextSplitter", "langchain_text_splitters.character.RecursiveCharacterTextSplitter", "langchain_text_splitters.base.TextSplitter", "langchain_core.documents.transformers.BaseDocumentTransformer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_text_splitters.markdown.MarkdownTextSplitter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["langchain_text_splitters.markdown.MarkdownTextSplitter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MarkdownTextSplitter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_text_splitters.markdown.MarkdownTextSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_text_splitters.markdown.MarkdownTextSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RecursiveCharacterTextSplitter": {".class": "SymbolTableNode", "cross_ref": "langchain_text_splitters.character.RecursiveCharacterTextSplitter", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.markdown.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.markdown.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.markdown.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.markdown.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.markdown.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_text_splitters.markdown.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_text_splitters/markdown.py"}