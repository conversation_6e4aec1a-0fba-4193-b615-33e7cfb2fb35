{"data_mtime": 1752086943, "dep_lines": [21, 22, 24, 230, 1, 3, 4, 5, 6, 7, 20, 197, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 641, 454, 640], "dep_prios": [5, 5, 5, 20, 5, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["langchain_core._api", "langchain_core.documents", "langchain_text_splitters.character", "bs4.element", "__future__", "copy", "pathlib", "re", "io", "typing", "requests", "bs4", "builtins", "_frozen_importlib", "_typeshed", "abc", "bs4.builder", "bs4.filter", "langchain_core", "langchain_core._api.beta_decorator", "langchain_core.documents.base", "langchain_core.documents.transformers", "langchain_core.load", "langchain_core.load.serializable", "langchain_text_splitters.base", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "typing_extensions"], "hash": "2ce5dbc569fc4cc20f36924b9c4fac003b325a0c", "id": "langchain_text_splitters.html", "ignore_all": true, "interface_hash": "3857afb623ef614230cff7a74aa37ce446e9d02a", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_text_splitters/html.py", "plugin_data": null, "size": 36835, "suppressed": ["nltk.corpus", "lxml", "nltk"], "version_id": "1.16.1"}