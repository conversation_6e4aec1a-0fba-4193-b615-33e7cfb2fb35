{"data_mtime": 1752086943, "dep_lines": [5, 1, 3, 1, 1, 1, 1, 1, 1, 23], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 20], "dependencies": ["langchain_text_splitters.base", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "langchain_core", "langchain_core.documents", "langchain_core.documents.transformers"], "hash": "f657b7eef288b50e9f3b0a881d605a5c8fb0d5c2", "id": "langchain_text_splitters.konlpy", "ignore_all": true, "interface_hash": "0adfbb8e191cae52a348600adc07a856f7693226", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_text_splitters/konlpy.py", "plugin_data": null, "size": 985, "suppressed": ["konlpy.tag"], "version_id": "1.16.1"}