{"data_mtime": 1752086944, "dep_lines": [25, 41, 42, 47, 60, 61, 62, 5, 18, 19, 23, 24, 31, 43, 48, 49, 54, 55, 72, 76, 1, 2, 3, 4, 6, 7, 8, 17, 63, 70, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.language_models.chat_models", "langchain_core.messages.ai", "langchain_core.messages.tool", "langchain_core.output_parsers.base", "langchain_core.utils.function_calling", "langchain_core.utils.pydantic", "langchain_core.utils.utils", "collections.abc", "langchain_core._api", "langchain_core.callbacks", "langchain_core.exceptions", "langchain_core.language_models", "langchain_core.messages", "langchain_core.output_parsers", "langchain_core.outputs", "langchain_core.runnables", "langchain_core.tools", "langchain_core.utils", "langchain_anthropic._client_utils", "langchain_anthropic.output_parsers", "copy", "json", "re", "warnings", "functools", "operator", "typing", "anthropic", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "anthropic._base_client", "anthropic._client", "anthropic._exceptions", "anthropic._models", "anthropic.types", "anthropic.types.raw_content_block_delta_event", "anthropic.types.raw_content_block_start_event", "anthropic.types.raw_content_block_stop_event", "anthropic.types.raw_message_delta_event", "anthropic.types.raw_message_start_event", "anthropic.types.raw_message_stop_event", "httpx", "httpx._models", "langchain_core", "langchain_core._api.beta_decorator", "langchain_core._api.deprecation", "langchain_core.caches", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.language_models.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.messages.human", "langchain_core.messages.system", "langchain_core.outputs.chat_generation", "langchain_core.outputs.chat_result", "langchain_core.outputs.generation", "langchain_core.prompt_values", "langchain_core.rate_limiters", "langchain_core.runnables.base", "langchain_core.tools.base", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "types"], "hash": "efce08a2ae60ca04f97b9e2d1a55eda858209878", "id": "langchain_anthropic.chat_models", "ignore_all": true, "interface_hash": "ce3781fc054f6609336aeda0309083cfe35be69c", "mtime": 1751599596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_anthropic/chat_models.py", "plugin_data": null, "size": 85812, "suppressed": [], "version_id": "1.16.1"}