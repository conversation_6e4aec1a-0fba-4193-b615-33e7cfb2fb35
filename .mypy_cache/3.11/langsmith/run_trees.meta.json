{"data_mtime": 1752086943, "dep_lines": [601, 606, 8, 14, 23, 25, 26, 27, 605, 3, 5, 6, 7, 9, 10, 11, 16, 21, 22, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 5, 5, 10, 10, 10, 5, 20, 5, 10, 10, 10, 5, 5, 5, 5, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.callbacks.manager", "langchain_core.tracers.langchain", "collections.abc", "pydantic.v1", "urllib.parse", "langsmith.schemas", "langsmith.utils", "langsmith.client", "langchain_core.runnables", "__future__", "json", "logging", "sys", "datetime", "typing", "uuid", "pydantic", "<PERSON><PERSON><PERSON>", "threading", "urllib", "langsmith", "builtins", "_contextvars", "_frozen_importlib", "_thread", "_typeshed", "abc", "annotated_types", "functools", "os", "pathlib", "pydantic._internal", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.types", "pydantic.v1.class_validators", "pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.utils", "re", "types", "typing_extensions"], "hash": "6196bc1e515845d966e8cf213babf2905716acd1", "id": "langsmith.run_trees", "ignore_all": true, "interface_hash": "3ba44899b349b141d9002dd8d85a87f282c32470", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/run_trees.py", "plugin_data": null, "size": 30490, "suppressed": [], "version_id": "1.16.1"}