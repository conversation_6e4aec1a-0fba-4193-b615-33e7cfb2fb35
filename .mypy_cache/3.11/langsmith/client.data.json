{".class": "MypyFile", "_fullname": "langsmith.client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AEVALUATOR_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.AEVALUATOR_T", "kind": "Gdef"}, "ATARGET_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._arunner.ATARGET_T", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncExperimentResults": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._arunner.AsyncExperimentResults", "kind": "Gdef"}, "AsyncIterable": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterable", "kind": "Gdef"}, "AttachmentInfo": {".class": "SymbolTableNode", "cross_ref": "langsmith.schemas.AttachmentInfo", "kind": "Gdef"}, "COMPARATIVE_EVALUATOR_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.COMPARATIVE_EVALUATOR_T", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langsmith.client.Client", "name": "Client", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langsmith.client.Client", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langsmith.client", "mro": ["langsmith.client.Client", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_url", "api_key", "retry_config", "timeout_ms", "web_url", "session", "auto_batch_tracing", "anonymizer", "hide_inputs", "hide_outputs", "hide_metadata", "info", "api_urls", "otel_tracer_provider", "tracing_sampling_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_url", "api_key", "retry_config", "timeout_ms", "web_url", "session", "auto_batch_tracing", "anonymizer", "hide_inputs", "hide_outputs", "hide_metadata", "info", "api_urls", "otel_tracer_provider", "tracing_sampling_rate"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["urllib3.util.retry.Retry", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["requests.sessions.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "langsmith.schemas.LangSmithInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.sdk.trace.TracerProvider", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "langsmith.client.Client.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_anonymizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._anonymizer", "name": "_anonymizer", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_api_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith.client.Client._api_key", "name": "_api_key", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_batch_ingest_run_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._batch_ingest_run_ops", "name": "_batch_ingest_run_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ops"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": ["langsmith._internal._operations.SerializedRunOperation"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_batch_ingest_run_ops of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compute_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._compute_headers", "name": "_compute_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_compute_headers of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_content_above_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._content_above_size", "name": "_content_above_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content_length"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_content_above_size of Client", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_create"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._create_run", "name": "_create_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_create"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_run of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_tenant_is_owner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._current_tenant_is_owner", "name": "_current_tenant_is_owner", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "owner"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_current_tenant_is_owner of Client", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_data_available_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._data_available_event", "name": "_data_available_event", "setter_type": null, "type": {".class": "UnionType", "items": ["threading.Event", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_filter_for_sampling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "runs", "patch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._filter_for_sampling", "name": "_filter_for_sampling", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "runs", "patch"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_filter_for_sampling of Client", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_filtered_post_uuids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._filtered_post_uuids", "name": "_filtered_post_uuids", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_futures": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._futures", "name": "_futures", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "_weakrefset.WeakSet"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_get_cursor_paginated_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "path", "body", "request_method", "data_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_cursor_paginated_list", "name": "_get_cursor_paginated_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "path", "body", "request_method", "data_key"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "GET"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "POST"}], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cursor_paginated_list of Client", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_data_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataset_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_data_type", "name": "_get_data_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dataset_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_data_type of Client", "ret_type": "langsmith.schemas.DataType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_data_type_cached": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._get_data_type_cached", "name": "_get_data_type_cached", "setter_type": null, "type": {".class": "Instance", "args": ["langsmith.schemas.DataType"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}, "_get_latest_commit_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "prompt_owner_and_name", "limit", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_latest_commit_hash", "name": "_get_latest_commit_hash", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "prompt_owner_and_name", "limit", "offset"], "arg_types": ["langsmith.client.Client", "builtins.str", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_latest_commit_hash of Client", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_optional_tenant_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_optional_tenant_id", "name": "_get_optional_tenant_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_optional_tenant_id of Client", "ret_type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_paginated_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "path", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_paginated_list", "name": "_get_paginated_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "path", "params"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_paginated_list of Client", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_prompt_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_prompt_url", "name": "_get_prompt_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_prompt_url of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_settings", "name": "_get_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_settings of Client", "ret_type": "langsmith.schemas.LangSmithSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tenant_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._get_tenant_id", "name": "_get_tenant_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_tenant_id of Client", "ret_type": "uuid.UUID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith.client.Client._headers", "name": "_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_hide_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._hide_inputs", "name": "_hide_inputs", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "uses_pep604_syntax": false}}}, "_hide_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._hide_metadata", "name": "_hide_metadata", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "uses_pep604_syntax": false}}}, "_hide_outputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._hide_outputs", "name": "_hide_outputs", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool"], "uses_pep604_syntax": false}}}, "_hide_run_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._hide_run_inputs", "name": "_hide_run_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_hide_run_inputs of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hide_run_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._hide_run_metadata", "name": "_hide_run_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "metadata"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_hide_run_metadata of Client", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hide_run_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._hide_run_outputs", "name": "_hide_run_outputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "outputs"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_hide_run_outputs of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client._host", "name": "_host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_host of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._host", "name": "_host", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_host of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_host_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client._host_url", "name": "_host_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_host_url of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._host_url", "name": "_host_url", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_host_url of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._info", "name": "_info", "setter_type": null, "type": {".class": "UnionType", "items": ["langsmith.schemas.LangSmithInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_insert_runtime_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["runs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "langsmith.client.Client._insert_runtime_env", "name": "_insert_runtime_env", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["runs"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_insert_runtime_env of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._insert_runtime_env", "name": "_insert_runtime_env", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["runs"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_insert_runtime_env of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_like_or_unlike_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prompt_identifier", "like"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._like_or_unlike_prompt", "name": "_like_or_unlike_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prompt_identifier", "like"], "arg_types": ["langsmith.client.Client", "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_like_or_unlike_prompt of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_child_runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._load_child_runs", "name": "_load_child_runs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langsmith.client.Client", "langsmith.schemas.Run"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_child_runs of Client", "ret_type": "langsmith.schemas.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_evaluation_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5], "arg_names": ["self", "evaluator_response", "run", "source_info", "project_id", "_executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._log_evaluation_feedback", "name": "_log_evaluation_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5], "arg_names": ["self", "evaluator_response", "run", "source_info", "project_id", "_executor"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.evaluation.evaluator.EvaluationResult", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.EvaluationResults"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.Run", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["concurrent.futures.thread.ThreadPoolExecutor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_log_evaluation_feedback of Client", "ret_type": {".class": "Instance", "args": ["langsmith.evaluation.evaluator.EvaluationResult"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_manual_cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith.client.Client._manual_cleanup", "name": "_manual_cleanup", "setter_type": null, "type": "builtins.bool"}}, "_multipart_ingest_ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._multipart_ingest_ops", "name": "_multipart_ingest_ops", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ops"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_multipart_ingest_ops of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_owner_conflict_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "action", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._owner_conflict_error", "name": "_owner_conflict_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "action", "owner"], "arg_types": ["langsmith.client.Client", "builtins.str", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_owner_conflict_error of Client", "ret_type": "langsmith.utils.LangSmithUserError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_post_batch_ingest_runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "body", "_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._post_batch_ingest_runs", "name": "_post_batch_ingest_runs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "body", "_context"], "arg_types": ["langsmith.client.Client", "builtins.bytes", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_post_batch_ingest_runs of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_multipart_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "examples", "include_dataset_id", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._prepare_multipart_data", "name": "_prepare_multipart_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "examples", "include_dataset_id", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.ExampleCreate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["langsmith.schemas.ExampleUpsertWithAttachments"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["langsmith.schemas.ExampleUpdate"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_multipart_data of Client", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes", {".class": "Instance", "args": ["builtins.str", "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prompt_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._prompt_exists", "name": "_prompt_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prompt_exists of Client", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pyo3_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._pyo3_client", "name": "_pyo3_client", "setter_type": null, "type": {".class": "NoneType"}}}, "_repr_html_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._repr_html_", "name": "_repr_html_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_repr_html_ of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_example_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "example", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._resolve_example_id", "name": "_resolve_example_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "example", "run"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.schemas.Example", "builtins.str", "uuid.UUID", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langsmith.schemas.Run"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_resolve_example_id of Client", "ret_type": {".class": "UnionType", "items": ["langsmith.schemas.Example", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_run_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "load_child_runs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._resolve_run_id", "name": "_resolve_run_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "load_child_runs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.schemas.Run", "langsmith.schemas.RunBase", "builtins.str", "uuid.UUID"], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_resolve_run_id of Client", "ret_type": "langsmith.schemas.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_run_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "run", "update", "copy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._run_transform", "name": "_run_transform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "run", "update", "copy"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.schemas.Run", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.RunLikeDict"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_run_transform of Client", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_select_eval_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "results", "fn_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._select_eval_results", "name": "_select_eval_results", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "results", "fn_name"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.evaluation.evaluator.EvaluationResult", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.EvaluationResults"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_select_eval_results of Client", "ret_type": {".class": "Instance", "args": ["langsmith.evaluation.evaluator.EvaluationResult"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_send_compressed_multipart_req": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "data_stream", "compressed_traces_info", "attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._send_compressed_multipart_req", "name": "_send_compressed_multipart_req", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "data_stream", "compressed_traces_info", "attempts"], "arg_types": ["langsmith.client.Client", "_io.BytesIO", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_send_compressed_multipart_req of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_send_multipart_req": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "acc", "attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._send_multipart_req", "name": "_send_multipart_req", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "acc", "attempts"], "arg_types": ["langsmith.client.Client", "langsmith._internal._multipart.MultipartPartsAndContext", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_send_multipart_req of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._settings", "name": "_settings", "setter_type": null, "type": {".class": "UnionType", "items": ["langsmith.schemas.LangSmithSettings", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_should_sample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._should_sample", "name": "_should_sample", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_should_sample of Client", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tenant_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._tenant_id", "name": "_tenant_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith.client.Client._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_update_examples_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "updates", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._update_examples_multipart", "name": "_update_examples_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "updates", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.ExampleUpdate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update_examples_multipart of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.UpsertExamplesResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._update_run", "name": "_update_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_update"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update_run of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_upload_examples_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "uploads", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client._upload_examples_multipart", "name": "_upload_examples_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "uploads", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.ExampleCreate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_upload_examples_multipart of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.UpsertExamplesResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_web_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client._web_url", "name": "_web_url", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_write_api_urls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client._write_api_urls", "name": "_write_api_urls", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "add_runs_to_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "queue_id", "run_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.add_runs_to_annotation_queue", "name": "add_runs_to_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "queue_id", "run_ids"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_runs_to_annotation_queue of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aevaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "error_handling", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "langsmith.client.Client.aevaluate", "name": "aevaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "error_handling", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._arunner.ATARGET_T"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, "builtins.str", "uuid.UUID", "langsmith.schemas.TracerSession"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "Instance", "args": ["langsmith.schemas.Example"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}, {".class": "Instance", "args": ["langsmith.schemas.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EVALUATOR_T"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.AEVALUATOR_T"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["langsmith.schemas.TracerSession", "builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "log"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aevaluate of Client", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "langsmith.evaluation._arunner.AsyncExperimentResults"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aevaluate_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "run", "evaluator", "source_info", "reference_example", "load_child_runs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "langsmith.client.Client.aevaluate_run", "name": "aevaluate_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "run", "evaluator", "source_info", "reference_example", "load_child_runs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.schemas.Run", "builtins.str", "uuid.UUID"], "uses_pep604_syntax": false}, "langsmith.evaluation.evaluator.RunEvaluator", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.Example", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aevaluate_run of Client", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "langsmith.evaluation.evaluator.EvaluationResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "api_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "langsmith.client.Client.api_key", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.api_key", "name": "api_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_key of Client", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.api_key", "name": "api_key", "setter_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_key of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_key of Client", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.api_key", "name": "api_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_key of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "", "name": "api_key", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_key of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": 1, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "api_key of Client", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "api_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client.api_url", "name": "api_url", "setter_type": null, "type": "builtins.str"}}, "arun_on_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "llm_or_chain_factory", "evaluation", "concurrency_level", "project_name", "project_metadata", "dataset_version", "verbose", "input_mapper", "revision_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "langsmith.client.Client.arun_on_dataset", "name": "arun_on_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "llm_or_chain_factory", "evaluation", "concurrency_level", "project_name", "project_metadata", "dataset_version", "verbose", "input_mapper", "revision_id", "kwargs"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "arun_on_dataset of Client", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_ingest_runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "create", "update", "pre_sampled"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.batch_ingest_runs", "name": "batch_ingest_runs", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "create", "update", "pre_sampled"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith.schemas.Run", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.RunLikeDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith.schemas.Run", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.RunLikeDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_ingest_runs of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.cleanup", "name": "cleanup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cleanup of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clone_public_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "token_or_url", "source_api_url", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.clone_public_dataset", "name": "clone_public_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "token_or_url", "source_api_url", "dataset_name"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clone_public_dataset of Client", "ret_type": "langsmith.schemas.Dataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compressed_traces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.compressed_traces", "name": "compressed_traces", "setter_type": null, "type": {".class": "UnionType", "items": ["langsmith._internal._compressed_traces.CompressedTraces", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "description", "queue_id", "rubric_instructions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_annotation_queue", "name": "create_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "name", "description", "queue_id", "rubric_instructions"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_annotation_queue of Client", "ret_type": "langsmith.schemas.AnnotationQueueWithDetails", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_chat_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "messages", "generations", "dataset_id", "dataset_name", "created_at"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.create_chat_example", "name": "create_chat_example", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "messages", "generations", "dataset_id", "dataset_name", "created_at"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "langsmith.schemas.BaseMessageLike"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "langsmith.schemas.BaseMessageLike", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_chat_example of Client", "ret_type": "langsmith.schemas.Example", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.create_chat_example", "name": "create_chat_example", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "create_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "prompt_identifier", "object", "parent_commit_hash"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_commit", "name": "create_commit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "prompt_identifier", "object", "parent_commit_hash"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_commit of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_comparative_experiment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "experiments", "reference_dataset", "description", "created_at", "metadata", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_comparative_experiment", "name": "create_comparative_experiment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "name", "experiments", "reference_dataset", "description", "created_at", "metadata", "id"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_comparative_experiment of Client", "ret_type": "langsmith.schemas.ComparativeExperiment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "dataset_name", "description", "data_type", "inputs_schema", "outputs_schema", "transformations", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_dataset", "name": "create_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "dataset_name", "description", "data_type", "inputs_schema", "outputs_schema", "transformations", "metadata"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "langsmith.schemas.DataType", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.DatasetTransformation"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_dataset of Client", "ret_type": "langsmith.schemas.Dataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "inputs", "dataset_id", "dataset_name", "created_at", "outputs", "metadata", "split", "example_id", "source_run_id", "use_source_run_io", "use_source_run_attachments", "attachments"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.create_example", "name": "create_example", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "inputs", "dataset_id", "dataset_name", "created_at", "outputs", "metadata", "split", "example_id", "source_run_id", "use_source_run_io", "use_source_run_attachments", "attachments"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.Attachments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_example of Client", "ret_type": "langsmith.schemas.Example", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.create_example", "name": "create_example", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "create_example_from_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "run", "dataset_id", "dataset_name", "created_at"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_example_from_run", "name": "create_example_from_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "run", "dataset_id", "dataset_name", "created_at"], "arg_types": ["langsmith.client.Client", "langsmith.schemas.Run", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_example_from_run of Client", "ret_type": "langsmith.schemas.Example", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "dataset_id", "examples", "dangerously_allow_filesystem", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.create_examples", "name": "create_examples", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "dataset_id", "examples", "dangerously_allow_filesystem", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith.schemas.ExampleCreate", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_examples of Client", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.UpsertExamplesResponse"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.create_examples", "name": "create_examples", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "create_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "run_id", "key", "score", "value", "trace_id", "correction", "comment", "source_info", "feedback_source_type", "source_run_id", "feedback_id", "feedback_config", "stop_after_attempt", "project_id", "comparative_experiment_id", "feedback_group_id", "extra", "error", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_feedback", "name": "create_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "run_id", "key", "score", "value", "trace_id", "correction", "comment", "source_info", "feedback_source_type", "source_run_id", "feedback_id", "feedback_config", "stop_after_attempt", "project_id", "comparative_experiment_id", "feedback_group_id", "extra", "error", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.FeedbackSourceType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.FeedbackConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_feedback of Client", "ret_type": "langsmith.schemas.Feedback", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_feedback_from_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "token_or_url", "score", "value", "correction", "comment", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_feedback_from_token", "name": "create_feedback_from_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "token_or_url", "score", "value", "correction", "comment", "metadata"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", "uuid.UUID"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_feedback_from_token of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_llm_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "prompt", "generation", "dataset_id", "dataset_name", "created_at"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.create_llm_example", "name": "create_llm_example", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "prompt", "generation", "dataset_id", "dataset_name", "created_at"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_llm_example of Client", "ret_type": "langsmith.schemas.Example", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.create_llm_example", "name": "create_llm_example", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "create_presigned_feedback_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "run_id", "feedback_key", "expiration", "feedback_config", "feedback_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_presigned_feedback_token", "name": "create_presigned_feedback_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "run_id", "feedback_key", "expiration", "feedback_config", "feedback_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.str", {".class": "UnionType", "items": ["datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.FeedbackConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_presigned_feedback_token of Client", "ret_type": "langsmith.schemas.FeedbackIngestToken", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_presigned_feedback_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "run_id", "feedback_keys", "expiration", "feedback_configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_presigned_feedback_tokens", "name": "create_presigned_feedback_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "run_id", "feedback_keys", "expiration", "feedback_configs"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["datetime.datetime", "datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.FeedbackConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_presigned_feedback_tokens of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.FeedbackIngestToken"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "project_name", "description", "metadata", "upsert", "project_extra", "reference_dataset_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_project", "name": "create_project", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "project_name", "description", "metadata", "upsert", "project_extra", "reference_dataset_id"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_project of Client", "ret_type": "langsmith.schemas.TracerSession", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "description", "readme", "tags", "is_public"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_prompt", "name": "create_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "description", "readme", "tags", "is_public"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_prompt of Client", "ret_type": "langsmith.schemas.Prompt", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "name", "inputs", "run_type", "project_name", "revision_id", "dangerously_allow_filesystem", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.create_run", "name": "create_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "name", "inputs", "run_type", "project_name", "revision_id", "dangerously_allow_filesystem", "kwargs"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.RUN_TYPE_T"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_run of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "queue_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.delete_annotation_queue", "name": "delete_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "queue_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_annotation_queue of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.delete_dataset", "name": "delete_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_dataset of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.delete_dataset", "name": "delete_dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "delete_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "example_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.delete_example", "name": "delete_example", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "example_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_example of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "example_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.delete_examples", "name": "delete_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "example_ids"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_examples of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "feedback_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.delete_feedback", "name": "delete_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "feedback_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_feedback of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "project_name", "project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.delete_project", "name": "delete_project", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "project_name", "project_id"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_project of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.delete_project", "name": "delete_project", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "delete_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.delete_prompt", "name": "delete_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_prompt of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_run_from_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "queue_id", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.delete_run_from_annotation_queue", "name": "delete_run_from_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "queue_id", "run_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_run_from_annotation_queue of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "diff_dataset_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 3, 3], "arg_names": ["self", "dataset_id", "dataset_name", "from_version", "to_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.diff_dataset_versions", "name": "diff_dataset_versions", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 3, 3], "arg_names": ["self", "dataset_id", "dataset_name", "from_version", "to_version"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "datetime.datetime"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "datetime.datetime"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "diff_dataset_versions of Client", "ret_type": "langsmith.schemas.DatasetDiffInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "langsmith.client.Client.evaluate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "error_handling", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "langsmith.client.Client.evaluate", "name": "evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "error_handling", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.TARGET_T"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.COMPARATIVE_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "log"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": {".class": "UnionType", "items": ["langsmith.evaluation._runner.ExperimentResults", "langsmith.evaluation._runner.ComparativeExperimentResults"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.evaluate", "name": "evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.TARGET_T"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": "langsmith.evaluation._runner.ExperimentResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langsmith.client.Client.evaluate", "name": "evaluate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.TARGET_T"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": "langsmith.evaluation._runner.ExperimentResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.evaluate", "name": "evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.COMPARATIVE_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": "langsmith.evaluation._runner.ComparativeExperimentResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langsmith.client.Client.evaluate", "name": "evaluate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.COMPARATIVE_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": "langsmith.evaluation._runner.ComparativeExperimentResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.TARGET_T"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": "langsmith.evaluation._runner.ExperimentResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": [null, null, "data", "evaluators", "summary_evaluators", "metadata", "experiment_prefix", "description", "max_concurrency", "num_repetitions", "blocking", "experiment", "upload_results", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.DATA_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.COMPARATIVE_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation._runner.EXPERIMENT_T"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of Client", "ret_type": "langsmith.evaluation._runner.ComparativeExperimentResults", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "evaluate_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "run", "evaluator", "source_info", "reference_example", "load_child_runs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.evaluate_run", "name": "evaluate_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "run", "evaluator", "source_info", "reference_example", "load_child_runs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["langsmith.schemas.Run", "langsmith.schemas.RunBase", "builtins.str", "uuid.UUID"], "uses_pep604_syntax": false}, "langsmith.evaluation.evaluator.RunEvaluator", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.Example", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate_run of Client", "ret_type": "langsmith.evaluation.evaluator.EvaluationResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "flush of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flush_compressed_traces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.flush_compressed_traces", "name": "flush_compressed_traces", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "attempts"], "arg_types": ["langsmith.client.Client", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "flush_compressed_traces of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.get_prompt", "name": "get_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_prompt of Client", "ret_type": {".class": "UnionType", "items": ["langsmith.schemas.Prompt", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_run_from_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "queue_id", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.get_run_from_annotation_queue", "name": "get_run_from_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "queue_id", "index"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_run_from_annotation_queue of Client", "ret_type": "langsmith.schemas.RunWithAnnotationQueueInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_run_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "id", "trace", "parent_run", "run_type", "project_names", "project_ids", "reference_example_ids", "start_time", "end_time", "error", "query", "filter", "trace_filter", "tree_filter", "is_root", "data_source_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.get_run_stats", "name": "get_run_stats", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "id", "trace", "parent_run", "run_type", "project_names", "project_ids", "reference_example_ids", "start_time", "end_time", "error", "query", "filter", "trace_filter", "tree_filter", "is_root", "data_source_type"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_run_stats of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_run_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "run", "project_name", "project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.get_run_url", "name": "get_run_url", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "run", "project_name", "project_id"], "arg_types": ["langsmith.client.Client", "langsmith.schemas.RunBase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_run_url of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_test_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "project_id", "project_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.get_test_results", "name": "get_test_results", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "project_id", "project_name"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_test_results of Client", "ret_type": {".class": "AnyType", "missing_import_name": "langsmith.client.pd", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "dataset_name", "dataset_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.has_dataset", "name": "has_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "dataset_name", "dataset_id"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_dataset of Client", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "project_name", "project_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.has_project", "name": "has_project", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "project_name", "project_id"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_project of Client", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 4], "arg_names": ["self", "dataset_id", "tag", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.index_dataset", "name": "index_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 4], "arg_names": ["self", "dataset_id", "tag", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "index_dataset of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.index_dataset", "name": "index_dataset", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "index_dataset of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "info of Client", "ret_type": "langsmith.schemas.LangSmithInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.info", "name": "info", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "info of Client", "ret_type": "langsmith.schemas.LangSmithInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "like_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.like_prompt", "name": "like_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "like_prompt of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_annotation_queues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "queue_ids", "name", "name_contains", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_annotation_queues", "name": "list_annotation_queues", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "queue_ids", "name", "name_contains", "limit"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_annotation_queues of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.AnnotationQueue"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_dataset_splits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name", "as_of"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_dataset_splits", "name": "list_dataset_splits", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name", "as_of"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_dataset_splits of Client", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_dataset_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name", "search", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_dataset_versions", "name": "list_dataset_versions", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name", "search", "limit"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_dataset_versions of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.DatasetVersion"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "dataset_ids", "data_type", "dataset_name", "dataset_name_contains", "metadata", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_datasets", "name": "list_datasets", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "dataset_ids", "data_type", "dataset_name", "dataset_name_contains", "metadata", "limit"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_datasets of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.Dataset"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_id", "dataset_name", "example_ids", "as_of", "splits", "inline_s3_urls", "offset", "limit", "metadata", "filter", "include_attachments", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_examples", "name": "list_examples", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_id", "dataset_name", "example_ids", "as_of", "splits", "inline_s3_urls", "offset", "limit", "metadata", "filter", "include_attachments", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_examples of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.Example"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "run_ids", "feedback_key", "feedback_source_type", "limit", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_feedback", "name": "list_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "run_ids", "feedback_key", "feedback_source_type", "limit", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.FeedbackSourceType"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_feedback of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.Feedback"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_presigned_feedback_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "run_id", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_presigned_feedback_tokens", "name": "list_presigned_feedback_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "run_id", "limit"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_presigned_feedback_tokens of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.FeedbackIngestToken"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_projects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "project_ids", "name", "name_contains", "reference_dataset_id", "reference_dataset_name", "reference_free", "limit", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_projects", "name": "list_projects", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "project_ids", "name", "name_contains", "reference_dataset_id", "reference_dataset_name", "reference_free", "limit", "metadata"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_projects of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.TracerSession"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_prompt_commits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "limit", "offset", "include_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_prompt_commits", "name": "list_prompt_commits", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "limit", "offset", "include_model"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_prompt_commits of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.ListedPromptCommit"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_prompts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "limit", "offset", "is_public", "is_archived", "sort_field", "sort_direction", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_prompts", "name": "list_prompts", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "limit", "offset", "is_public", "is_archived", "sort_field", "sort_direction", "query"], "arg_types": ["langsmith.client.Client", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "langsmith.schemas.PromptSortField", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_prompts of Client", "ret_type": "langsmith.schemas.ListPromptsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "project_id", "project_name", "run_type", "trace_id", "reference_example_id", "query", "filter", "trace_filter", "tree_filter", "is_root", "parent_run_id", "start_time", "error", "run_ids", "select", "limit", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_runs", "name": "list_runs", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "project_id", "project_name", "run_type", "trace_id", "reference_example_id", "query", "filter", "trace_filter", "tree_filter", "is_root", "parent_run_id", "start_time", "error", "run_ids", "select", "limit", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_runs of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.Run"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_shared_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "share_token", "example_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_shared_examples", "name": "list_shared_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "share_token", "example_ids"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_shared_examples of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.Example"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_shared_projects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["self", "dataset_share_token", "project_ids", "name", "name_contains", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_shared_projects", "name": "list_shared_projects", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["self", "dataset_share_token", "project_ids", "name", "name_contains", "limit"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_shared_projects of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.TracerSessionResult"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_shared_runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "share_token", "run_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.list_shared_runs", "name": "list_shared_runs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "share_token", "run_ids"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_shared_runs of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.Run"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multipart_ingest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "create", "update", "pre_sampled", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.multipart_ingest", "name": "multipart_ingest", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["self", "create", "update", "pre_sampled", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith.schemas.Run", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.RunLikeDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith.schemas.Run", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.RunLikeDict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "multipart_ingest of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "otel_exporter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.otel_exporter", "name": "otel_exporter", "setter_type": null, "type": {".class": "UnionType", "items": ["langsmith._internal.otel._otel_exporter.OTELExporter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pull_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "prompt_identifier", "include_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.pull_prompt", "name": "pull_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "prompt_identifier", "include_model"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pull_prompt of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pull_prompt_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "prompt_identifier", "include_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.pull_prompt_commit", "name": "pull_prompt_commit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "prompt_identifier", "include_model"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pull_prompt_commit of Client", "ret_type": "langsmith.schemas.PromptCommit", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "object", "parent_commit_hash", "is_public", "description", "readme", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.push_prompt", "name": "push_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "object", "parent_commit_hash", "is_public", "description", "readme", "tags"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "push_prompt of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "queue_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_annotation_queue", "name": "read_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "queue_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_annotation_queue of Client", "ret_type": "langsmith.schemas.AnnotationQueue", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "dataset_name", "dataset_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.read_dataset", "name": "read_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "dataset_name", "dataset_id"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_dataset of Client", "ret_type": "langsmith.schemas.Dataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.read_dataset", "name": "read_dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "read_dataset_openai_finetuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_dataset_openai_finetuning", "name": "read_dataset_openai_finetuning", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_dataset_openai_finetuning of Client", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_dataset_shared_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_dataset_shared_schema", "name": "read_dataset_shared_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_dataset_shared_schema of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.DatasetShareSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_dataset_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name", "as_of", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_dataset_version", "name": "read_dataset_version", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "dataset_id", "dataset_name", "as_of", "tag"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_dataset_version of Client", "ret_type": "langsmith.schemas.DatasetVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "example_id", "as_of"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_example", "name": "read_example", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "example_id", "as_of"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_example of Client", "ret_type": "langsmith.schemas.Example", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "feedback_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_feedback", "name": "read_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "feedback_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_feedback of Client", "ret_type": "langsmith.schemas.Feedback", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "project_id", "project_name", "include_stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.read_project", "name": "read_project", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "project_id", "project_name", "include_stats"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_project of Client", "ret_type": "langsmith.schemas.TracerSessionResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.read_project", "name": "read_project", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "read_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "run_id", "load_child_runs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_run", "name": "read_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "run_id", "load_child_runs"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_run of Client", "ret_type": "langsmith.schemas.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_run_shared_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_run_shared_link", "name": "read_run_shared_link", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_run_shared_link of Client", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_shared_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "share_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_shared_dataset", "name": "read_shared_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "share_token"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_shared_dataset of Client", "ret_type": "langsmith.schemas.Dataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_shared_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "share_token", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.read_shared_run", "name": "read_shared_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "share_token", "run_id"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_shared_run of Client", "ret_type": "langsmith.schemas.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request_with_retries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "method", "pathname", "request_kwargs", "stop_after_attempt", "retry_on", "to_ignore", "handle_response", "_context", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.request_with_retries", "name": "request_with_retries", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "method", "pathname", "request_kwargs", "stop_after_attempt", "retry_on", "to_ignore", "handle_response", "_context", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "GET"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "POST"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "PUT"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "PATCH"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "DELETE"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["requests.models.Response", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request_with_retries of Client", "ret_type": "requests.models.Response", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retry_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client.retry_config", "name": "retry_config", "setter_type": null, "type": "urllib3.util.retry.Retry"}}, "run_is_shared": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.run_is_shared", "name": "run_is_shared", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run_is_shared of Client", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_on_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "llm_or_chain_factory", "evaluation", "concurrency_level", "project_name", "project_metadata", "dataset_version", "verbose", "input_mapper", "revision_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.run_on_dataset", "name": "run_on_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "llm_or_chain_factory", "evaluation", "concurrency_level", "project_name", "project_metadata", "dataset_version", "verbose", "input_mapper", "revision_id", "kwargs"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run_on_dataset of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client.session", "name": "session", "setter_type": null, "type": "requests.sessions.Session"}}, "share_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.share_dataset", "name": "share_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "dataset_id", "dataset_name"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "share_dataset of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.DatasetShareSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "share_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "run_id", "share_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.share_run", "name": "share_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "run_id", "share_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "share_run of Client", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "similar_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 5, 4], "arg_names": [null, null, "limit", "dataset_id", "filter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.similar_examples", "name": "similar_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 5, 4], "arg_names": [null, null, "limit", "dataset_id", "filter", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similar_examples of Client", "ret_type": {".class": "Instance", "args": ["langsmith.schemas.ExampleSearch"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.similar_examples", "name": "similar_examples", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "similar_examples of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sync_indexed_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 4], "arg_names": ["self", "dataset_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langsmith.client.Client.sync_indexed_dataset", "name": "sync_indexed_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 3, 4], "arg_names": ["self", "dataset_id", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sync_indexed_dataset of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.sync_indexed_dataset", "name": "sync_indexed_dataset", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "sync_indexed_dataset of Client", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timeout_ms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client.timeout_ms", "name": "timeout_ms", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "tracing_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langsmith.client.Client.tracing_queue", "name": "tracing_queue", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "queue.PriorityQueue"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tracing_sample_rate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client.Client.tracing_sample_rate", "name": "tracing_sample_rate", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "unlike_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.unlike_prompt", "name": "unlike_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prompt_identifier"], "arg_types": ["langsmith.client.Client", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unlike_prompt of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unshare_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataset_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.unshare_dataset", "name": "unshare_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dataset_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unshare_dataset of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unshare_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.unshare_run", "name": "unshare_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_id"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "unshare_run of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_annotation_queue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5], "arg_names": ["self", "queue_id", "name", "description", "rubric_instructions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_annotation_queue", "name": "update_annotation_queue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5], "arg_names": ["self", "queue_id", "name", "description", "rubric_instructions"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_annotation_queue of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_dataset_splits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 3, 3, 5], "arg_names": ["self", "dataset_id", "dataset_name", "split_name", "example_ids", "remove"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_dataset_splits", "name": "update_dataset_splits", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 3, 3, 5], "arg_names": ["self", "dataset_id", "dataset_name", "split_name", "example_ids", "remove"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_dataset_splits of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_dataset_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 3, 3], "arg_names": ["self", "dataset_id", "dataset_name", "as_of", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_dataset_tag", "name": "update_dataset_tag", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 3, 3], "arg_names": ["self", "dataset_id", "dataset_name", "as_of", "tag"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "datetime.datetime", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_dataset_tag of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "example_id", "inputs", "outputs", "metadata", "split", "dataset_id", "attachments_operations", "attachments"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_example", "name": "update_example", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "example_id", "inputs", "outputs", "metadata", "split", "dataset_id", "attachments_operations", "attachments"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.AttachmentsOperations", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.Attachments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_example of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "dataset_id", "updates", "dangerously_allow_filesystem", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_examples", "name": "update_examples", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 4], "arg_names": ["self", "dataset_name", "dataset_id", "updates", "dangerously_allow_filesystem", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith.schemas.ExampleUpdate", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_examples of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_examples_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "updates", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_examples_multipart", "name": "update_examples_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "updates", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.ExampleUpdate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_examples_multipart of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.UpsertExamplesResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "feedback_id", "score", "value", "correction", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_feedback", "name": "update_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "feedback_id", "score", "value", "correction", "comment"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_feedback of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "project_id", "name", "description", "metadata", "project_extra", "end_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_project", "name": "update_project", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "project_id", "name", "description", "metadata", "project_extra", "end_time"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_project of Client", "ret_type": "langsmith.schemas.TracerSession", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "description", "readme", "tags", "is_public", "is_archived"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_prompt", "name": "update_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "prompt_identifier", "description", "readme", "tags", "is_public", "is_archived"], "arg_types": ["langsmith.client.Client", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_prompt of Client", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "run_id", "name", "end_time", "error", "inputs", "outputs", "events", "extra", "tags", "attachments", "dangerously_allow_filesystem", "reference_example_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.update_run", "name": "update_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "run_id", "name", "end_time", "error", "inputs", "outputs", "events", "extra", "tags", "attachments", "dangerously_allow_filesystem", "reference_example_id", "kwargs"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.Attachments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_run of Client", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload_csv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "csv_file", "input_keys", "output_keys", "name", "description", "data_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.upload_csv", "name": "upload_csv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "csv_file", "input_keys", "output_keys", "name", "description", "data_type"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "_io.BytesIO"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.DataType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_csv of Client", "ret_type": "langsmith.schemas.Dataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload_dataframe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5], "arg_names": ["self", "df", "name", "input_keys", "output_keys", "description", "data_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.upload_dataframe", "name": "upload_dataframe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 5], "arg_names": ["self", "df", "name", "input_keys", "output_keys", "description", "data_type"], "arg_types": ["langsmith.client.Client", {".class": "AnyType", "missing_import_name": "langsmith.client.pd", "source_any": null, "type_of_any": 3}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.schemas.DataType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_dataframe of Client", "ret_type": "langsmith.schemas.Dataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload_examples_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "uploads", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.upload_examples_multipart", "name": "upload_examples_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5], "arg_names": ["self", "dataset_id", "uploads", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.ExampleCreate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_examples_multipart of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.UpsertExamplesResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upsert_examples_multipart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "upserts", "dangerously_allow_filesystem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client.Client.upsert_examples_multipart", "name": "upsert_examples_multipart", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "upserts", "dangerously_allow_filesystem"], "arg_types": ["langsmith.client.Client", {".class": "UnionType", "items": [{".class": "Instance", "args": ["langsmith.schemas.ExampleUpsertWithAttachments"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert_examples_multipart of Client", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.UpsertExamplesResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langsmith.client.Client.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langsmith.client.Client", "values": [], "variance": 0}, "slots": ["__weakref__", "_anonymizer", "_api_key", "_data_available_event", "_filtered_post_uuids", "_futures", "_get_data_type_cached", "_headers", "_hide_inputs", "_hide_metadata", "_hide_outputs", "_info", "_manual_cleanup", "_pyo3_client", "_settings", "_tenant_id", "_timeout", "_web_url", "_write_api_urls", "api_url", "compressed_traces", "otel_exporter", "retry_config", "session", "timeout_ms", "tracing_queue", "tracing_sample_rate"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComparativeExperimentResults": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.ComparativeExperimentResults", "kind": "Gdef"}, "CompressedTraces": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._compressed_traces.CompressedTraces", "kind": "Gdef"}, "DATA_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.DATA_T", "kind": "Gdef"}, "EMPTY_SEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langsmith.client.EMPTY_SEQ", "name": "EMPTY_SEQ", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "EVALUATOR_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.EVALUATOR_T", "kind": "Gdef"}, "EXPERIMENT_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.EXPERIMENT_T", "kind": "Gdef"}, "ExperimentResults": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.ExperimentResults", "kind": "Gdef"}, "HAS_OTEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith.client.HAS_OTEL", "name": "HAS_OTEL", "setter_type": null, "type": "builtins.bool"}}, "ID_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langsmith.client.ID_TYPE", "line": 206, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["uuid.UUID", "builtins.str"], "uses_pep604_syntax": false}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MultipartPart": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._multipart.MultipartPart", "kind": "Gdef"}, "MultipartPartsAndContext": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._multipart.MultipartPartsAndContext", "kind": "Gdef"}, "OTELExporter": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal.otel._otel_exporter.OTELExporter", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PoolKey": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolKey", "kind": "Gdef"}, "PriorityQueue": {".class": "SymbolTableNode", "cross_ref": "queue.PriorityQueue", "kind": "Gdef"}, "RUN_TYPE_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langsmith.client.RUN_TYPE_T", "line": 207, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "llm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "retriever"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "embedding"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "prompt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parser"}], "uses_pep604_syntax": false}}}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "Runnable": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.Runnable", "kind": "Gdef"}, "SUMMARY_EVALUATOR_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation.evaluator.SUMMARY_EVALUATOR_T", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SerializedFeedbackOperation": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.SerializedFeedbackOperation", "kind": "Gdef"}, "SerializedRunOperation": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.SerializedRunOperation", "kind": "Gdef"}, "TARGET_T": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation._runner.TARGET_T", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracerProvider": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.sdk.trace.TracerProvider", "kind": "Gdef"}, "TracingQueueItem": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._background_thread.TracingQueueItem", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "URLLIB3_SUPPORTS_BLOCKSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith.client.URLLIB3_SUPPORTS_BLOCKSIZE", "name": "URLLIB3_SUPPORTS_BLOCKSIZE", "setter_type": null, "type": "builtins.bool"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "X_API_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith.client.X_API_KEY", "name": "X_API_KEY", "setter_type": null, "type": "builtins.str"}}, "ZoneInfo": {".class": "SymbolTableNode", "cross_ref": "zoneinfo.ZoneInfo", "kind": "Gdef"}, "_AUTO_SCALE_UP_NTHREADS_LIMIT": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._AUTO_SCALE_UP_NTHREADS_LIMIT", "kind": "Gdef"}, "_BLOCKSIZE_BYTES": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._BLOCKSIZE_BYTES", "kind": "Gdef"}, "_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._BOUNDARY", "kind": "Gdef"}, "_LangSmithHttpAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["requests.adapters.HTTPAdapter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langsmith.client._LangSmithHttpAdapter", "name": "_LangSmithHttpAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langsmith.client._LangSmithHttpAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langsmith.client", "mro": ["langsmith.client._LangSmithHttpAdapter", "requests.adapters.HTTPAdapter", "requests.adapters.BaseAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__attrs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "langsmith.client._LangSmithHttpAdapter.__attrs__", "name": "__attrs__", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pool_connections", "pool_maxsize", "max_retries", "pool_block", "blocksize"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith.client._LangSmithHttpAdapter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pool_connections", "pool_maxsize", "max_retries", "pool_block", "blocksize"], "arg_types": ["langsmith.client._LangSmithHttpAdapter", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _LangSmithHttpAdapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_blocksize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith.client._LangSmithHttpAdapter._blocksize", "name": "_blocksize", "setter_type": null, "type": "builtins.int"}}, "init_poolmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "connections", "maxsize", "block", "pool_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._LangSmithHttpAdapter.init_poolmanager", "name": "init_poolmanager", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langsmith.client._LangSmithHttpAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langsmith.client._LangSmithHttpAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SIZE_LIMIT_BYTES": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._SIZE_LIMIT_BYTES", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith.client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith.client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith.client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith.client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith.client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith.client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_as_uuid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["value", "var"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._as_uuid", "name": "_as_uuid", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["value", "var"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_as_uuid", "ret_type": "uuid.UUID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_close_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._close_files", "name": "_close_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["files"], "arg_types": [{".class": "Instance", "args": ["_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_close_files", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_construct_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["api_url", "pathname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._construct_url", "name": "_construct_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["api_url", "pathname"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_construct_url", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_stored_attachments_to_attachments_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["data", "attachments_key", "api_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._convert_stored_attachments_to_attachments_dict", "name": "_convert_stored_attachments_to_attachments_dict", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["data", "attachments_key", "api_url"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_stored_attachments_to_attachments_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.AttachmentInfo"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dataset_examples_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["api_url", "dataset_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._dataset_examples_path", "name": "_dataset_examples_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["api_url", "dataset_id"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.client.ID_TYPE"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_dataset_examples_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_retry_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langsmith.client._default_retry_config", "name": "_default_retry_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_default_retry_config", "ret_type": "urllib3.util.retry.Retry", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langsmith.client._default_retry_config", "name": "_default_retry_config", "setter_type": null, "type": {".class": "Instance", "args": ["urllib3.util.retry.Retry"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_dumps_json": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._serde.dumps_json", "kind": "Gdef"}, "_ensure_uuid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "langsmith.client._ensure_uuid", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["value", "accept_null"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "langsmith.client._ensure_uuid", "name": "_ensure_uuid", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["value", "accept_null"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "langsmith.client._ensure_uuid", "name": "_ensure_uuid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": "uuid.UUID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langsmith.client._ensure_uuid", "name": "_ensure_uuid", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": "uuid.UUID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["value", "accept_null"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "langsmith.client._ensure_uuid", "name": "_ensure_uuid", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["value", "accept_null"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langsmith.client._ensure_uuid", "name": "_ensure_uuid", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["value", "accept_null"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": "uuid.UUID", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["value", "accept_null"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_uuid", "ret_type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_format_feedback_score": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["score"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._format_feedback_score", "name": "_format_feedback_score", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["score"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_feedback_score", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tracing_sampling_rate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["tracing_sampling_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._get_tracing_sampling_rate", "name": "_get_tracing_sampling_rate", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["tracing_sampling_rate"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_tracing_sampling_rate", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_write_api_urls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_write_api_urls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._get_write_api_urls", "name": "_get_write_api_urls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_write_api_urls"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_write_api_urls", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_langchain_hosted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._is_langchain_hosted", "name": "_is_langchain_hosted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_langchain_hosted", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_orjson": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._or<PERSON>son", "kind": "Gdef"}, "_parse_token_or_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["url_or_token", "api_url", "num_parts", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._parse_token_or_url", "name": "_parse_token_or_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["url_or_token", "api_url", "num_parts", "kind"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "uuid.UUID"], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_token_or_url", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langsmith.client._parse_url", "name": "_parse_url", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langsmith.client._parse_url", "name": "_parse_url", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_tracing_control_thread_func": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._background_thread.tracing_control_thread_func", "kind": "Gdef"}, "_urllib3_logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith.client._urllib3_logger", "name": "_urllib3_logger", "setter_type": null, "type": "logging.Logger"}}, "_validate_api_key_if_hosted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["api_url", "api_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client._validate_api_key_if_hosted", "name": "_validate_api_key_if_hosted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["api_url", "api_key"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_api_key_if_hosted", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "atexit": {".class": "SymbolTableNode", "cross_ref": "atexit", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "cf": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures", "kind": "Gdef"}, "close_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client.close_session", "name": "close_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": ["requests.sessions.Session"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "combine_serialized_queue_operations": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.combine_serialized_queue_operations", "kind": "Gdef"}, "compress_multipart_parts_and_context": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.compress_multipart_parts_and_context", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "convert_prompt_to_anthropic_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["messages", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client.convert_prompt_to_anthropic_format", "name": "convert_prompt_to_anthropic_format", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["messages", "model_kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_prompt_to_anthropic_format", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_prompt_to_openai_format": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["messages", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client.convert_prompt_to_openai_format", "name": "convert_prompt_to_openai_format", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["messages", "model_kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_prompt_to_openai_format", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "dump_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith.client.dump_model", "name": "dump_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dump_model", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_otlp_tracer_provider": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal.otel._otel_client.get_otlp_tracer_provider", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "join_multipart_parts_and_context": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._multipart.join_multipart_parts_and_context", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "langsmith": {".class": "SymbolTableNode", "cross_ref": "langsmith", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith.client.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ls_env": {".class": "SymbolTableNode", "cross_ref": "langsmith.env", "kind": "Gdef"}, "ls_evaluator": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation.evaluator", "kind": "Gdef"}, "ls_schemas": {".class": "SymbolTableNode", "cross_ref": "langsmith.schemas", "kind": "Gdef"}, "ls_utils": {".class": "SymbolTableNode", "cross_ref": "langsmith.utils", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "otel_trace": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langsmith.client.pd", "name": "pd", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langsmith.client.pd", "source_any": null, "type_of_any": 3}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "requests_adapters": {".class": "SymbolTableNode", "cross_ref": "requests.adapters", "kind": "Gdef"}, "rqtb_multipart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langsmith.client.rqtb_multipart", "name": "rqtb_multipart", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langsmith.client.rqtb_multipart", "source_any": null, "type_of_any": 3}}}, "schemas": {".class": "SymbolTableNode", "cross_ref": "langsmith.schemas", "kind": "Gdef"}, "serialize_feedback_dict": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.serialize_feedback_dict", "kind": "Gdef"}, "serialize_run_dict": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.serialize_run_dict", "kind": "Gdef"}, "serialized_feedback_operation_to_multipart_parts_and_context": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.serialized_feedback_operation_to_multipart_parts_and_context", "kind": "Gdef"}, "serialized_run_operation_to_multipart_parts_and_context": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.serialized_run_operation_to_multipart_parts_and_context", "kind": "Gdef"}, "set_span_in_context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.propagation.set_span_in_context", "kind": "Gdef"}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "urllib_parse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "warn_beta": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._beta_decorator.warn_beta", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/client.py"}