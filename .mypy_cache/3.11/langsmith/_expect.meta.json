{"data_mtime": 1752086944, "dep_lines": [70, 71, 64, 65, 66, 67, 50, 52, 53, 54, 64, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 10, 10, 10, 10, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langsmith._internal._edit_distance", "langsmith._internal._embedding_distance", "langsmith.client", "langsmith.run_helpers", "langsmith.run_trees", "langsmith.utils", "__future__", "atexit", "inspect", "typing", "langsmith", "builtins", "_frozen_importlib", "abc", "concurrent", "concurrent.futures", "concurrent.futures._base", "concurrent.futures.thread", "enum", "langsmith._internal", "langsmith.schemas", "pydantic", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "types", "uuid"], "hash": "0d5c9acee7a9cf8e2ad5d0776c0835c240bda8a2", "id": "langsmith._expect", "ignore_all": true, "interface_hash": "210fcf0224e2ee6df10a37f64b67aa6971e48392", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_expect.py", "plugin_data": null, "size": 14971, "suppressed": [], "version_id": "1.16.1"}