{"data_mtime": 1752086943, "dep_lines": [10, 19, 6, 7, 8, 9, 11, 18, 20, 3, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 30, 30], "dependencies": ["langsmith.evaluation.evaluator", "langsmith.testing._internal", "langsmith._expect", "langsmith.async_client", "langsmith.client", "langsmith.evaluation", "langsmith.run_helpers", "langsmith.run_trees", "langsmith.utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "0acdb04caea80d7435a3322427495169ccf776a2", "id": "langsmith", "ignore_all": true, "interface_hash": "4d0d746b0190da3a5ee9f09eb7095857d1fd3adc", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/__init__.py", "plugin_data": null, "size": 3436, "suppressed": [], "version_id": "1.16.1"}