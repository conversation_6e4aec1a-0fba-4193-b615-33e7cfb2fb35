{"data_mtime": 1752086944, "dep_lines": [24, 1517, 1725, 1726, 1729, 1730, 10, 21, 22, 23, 24, 1727, 1728, 1737, 3, 5, 6, 7, 8, 9, 11, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 20, 20, 20, 20, 5, 10, 10, 10, 20, 20, 20, 20, 5, 10, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langsmith._internal._beta_decorator", "langchain_core.load.dump", "langchain_core.language_models.base", "langchain_core.load.load", "langchain_core.prompts.structured", "langchain_core.runnables.base", "collections.abc", "langsmith.client", "langsmith.schemas", "langsmith.utils", "langsmith._internal", "langchain_core.output_parsers", "langchain_core.prompts", "langchain_core._api", "__future__", "asyncio", "contextlib", "datetime", "json", "uuid", "typing", "httpx", "langsmith", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "asyncio.tasks", "enum", "http", "http.cookiejar", "httpx._auth", "httpx._client", "httpx._config", "httpx._models", "httpx._transports", "httpx._transports.base", "httpx._urls", "json.encoder", "langchain_core", "langchain_core._api.beta_decorator", "langchain_core.caches", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.language_models", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages", "langchain_core.messages.base", "langchain_core.output_parsers.base", "langchain_core.prompts.base", "langchain_core.prompts.chat", "langchain_core.prompts.message", "langchain_core.runnables", "langchain_core.runnables.config", "langsmith._internal._serde", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "requests", "requests.models", "ssl", "types", "typing_extensions"], "hash": "eab58c959544e39fb10929c7f6585acebcfc729b", "id": "langsmith.async_client", "ignore_all": true, "interface_hash": "f3bca84d85a579eb66b3a5dce38bab4cdc416156", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/async_client.py", "plugin_data": null, "size": 70694, "suppressed": [], "version_id": "1.16.1"}