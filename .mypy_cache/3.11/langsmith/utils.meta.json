{"data_mtime": 1752086943, "dep_lines": [18, 19, 29, 34, 36, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 29, 31, 32, 33, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "concurrent.futures", "urllib.parse", "urllib3.util", "langsmith.schemas", "__future__", "contextlib", "<PERSON><PERSON><PERSON>", "copy", "enum", "functools", "logging", "os", "pathlib", "socket", "subprocess", "sys", "threading", "traceback", "typing", "urllib", "httpx", "requests", "typing_extensions", "langsmith", "builtins", "_frozen_importlib", "_thread", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "httpx._models", "requests.models", "urllib3", "urllib3.util.retry"], "hash": "0c3ce282ee7c0b33397df00b763123bc6ce55456", "id": "langsmith.utils", "ignore_all": true, "interface_hash": "1085ebb85da5d636a79cf58bb45b46aa3654e54f", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/utils.py", "plugin_data": null, "size": 26469, "suppressed": [], "version_id": "1.16.1"}