{"data_mtime": 1752086944, "dep_lines": [38, 39, 16, 17, 31, 32, 33, 34, 36, 37, 38, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 18, 19, 29, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 43], "dep_prios": [10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["langsmith._internal._or<PERSON>son", "langsmith._internal._serde", "collections.abc", "concurrent.futures", "langsmith.client", "langsmith.env", "langsmith.run_helpers", "langsmith.run_trees", "langsmith.schemas", "langsmith.utils", "langsmith._internal", "__future__", "atexit", "contextlib", "<PERSON><PERSON><PERSON>", "datetime", "functools", "importlib", "inspect", "logging", "os", "threading", "time", "uuid", "warnings", "pathlib", "typing", "typing_extensions", "langsmith", "builtins", "_collections_abc", "_contextvars", "_frozen_importlib", "_thread", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "pydantic", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils"], "hash": "07815b13d692f8ba958bab6bb76b0000e7fea81c", "id": "langsmith.testing._internal", "ignore_all": true, "interface_hash": "336469738c8fcf58207a9ac6da52d2eecb64ae41", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/testing/_internal.py", "plugin_data": null, "size": 43311, "suppressed": ["pytest"], "version_id": "1.16.1"}