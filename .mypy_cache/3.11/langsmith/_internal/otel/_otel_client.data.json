{".class": "MypyFile", "_fullname": "langsmith._internal.otel._otel_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BatchSpanProcessor": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.sdk.trace.export.BatchSpanProcessor", "kind": "Gdef"}, "HAS_OTEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_client.HAS_OTEL", "name": "HAS_OTEL", "setter_type": null, "type": "builtins.bool"}}, "OTLPSpanExporter": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.exporter.otlp.proto.http.trace_exporter.OTLPSpanExporter", "kind": "Gdef"}, "Resource": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.sdk.resources.Resource", "kind": "Gdef"}, "SERVICE_NAME": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.sdk.resources.SERVICE_NAME", "kind": "Gdef"}, "TracerProvider": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.sdk.trace.TracerProvider", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "get_otlp_tracer_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal.otel._otel_client.get_otlp_tracer_provider", "name": "get_otlp_tracer_provider", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_otlp_tracer_provider", "ret_type": "opentelemetry.sdk.trace.TracerProvider", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ls_utils": {".class": "SymbolTableNode", "cross_ref": "langsmith.utils", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/otel/_otel_client.py"}