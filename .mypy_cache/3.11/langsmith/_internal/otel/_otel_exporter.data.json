{".class": "MypyFile", "_fullname": "langsmith._internal.otel._otel_exporter", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "GENAI_COMPLETION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GENAI_COMPLETION", "name": "GENAI_COMPLETION", "setter_type": null, "type": "builtins.str"}}, "GENAI_PROMPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GENAI_PROMPT", "name": "GENAI_PROMPT", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_ASSISTANT_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_ASSISTANT_MESSAGE", "name": "GEN_AI_ASSISTANT_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_CHOICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_CHOICE", "name": "GEN_AI_CHOICE", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_OPERATION_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_OPERATION_NAME", "name": "GEN_AI_OPERATION_NAME", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_EXTRA_BODY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_EXTRA_BODY", "name": "GEN_AI_REQUEST_EXTRA_BODY", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_EXTRA_QUERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_EXTRA_QUERY", "name": "GEN_AI_REQUEST_EXTRA_QUERY", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_FREQUENCY_PENALTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_FREQUENCY_PENALTY", "name": "GEN_AI_REQUEST_FREQUENCY_PENALTY", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_MAX_TOKENS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_MAX_TOKENS", "name": "GEN_AI_REQUEST_MAX_TOKENS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_MODEL", "name": "GEN_AI_REQUEST_MODEL", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_PRESENCE_PENALTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_PRESENCE_PENALTY", "name": "GEN_AI_REQUEST_PRESENCE_PENALTY", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_TEMPERATURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_TEMPERATURE", "name": "GEN_AI_REQUEST_TEMPERATURE", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_REQUEST_TOP_P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_REQUEST_TOP_P", "name": "GEN_AI_REQUEST_TOP_P", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_RESPONSE_FINISH_REASONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_RESPONSE_FINISH_REASONS", "name": "GEN_AI_RESPONSE_FINISH_REASONS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_RESPONSE_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_RESPONSE_ID", "name": "GEN_AI_RESPONSE_ID", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_RESPONSE_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_RESPONSE_MODEL", "name": "GEN_AI_RESPONSE_MODEL", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_RESPONSE_SERVICE_TIER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_RESPONSE_SERVICE_TIER", "name": "GEN_AI_RESPONSE_SERVICE_TIER", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_RESPONSE_SYSTEM_FINGERPRINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_RESPONSE_SYSTEM_FINGERPRINT", "name": "GEN_AI_RESPONSE_SYSTEM_FINGERPRINT", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_SERIALIZED_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_SERIALIZED_DOC", "name": "GEN_AI_SERIALIZED_DOC", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_SERIALIZED_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_SERIALIZED_NAME", "name": "GEN_AI_SERIALIZED_NAME", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_SERIALIZED_SIGNATURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_SERIALIZED_SIGNATURE", "name": "GEN_AI_SERIALIZED_SIGNATURE", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_SYSTEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_SYSTEM", "name": "GEN_AI_SYSTEM", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_SYSTEM_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_SYSTEM_MESSAGE", "name": "GEN_AI_SYSTEM_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_USAGE_INPUT_TOKENS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_USAGE_INPUT_TOKENS", "name": "GEN_AI_USAGE_INPUT_TOKENS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_USAGE_INPUT_TOKEN_DETAILS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_USAGE_INPUT_TOKEN_DETAILS", "name": "GEN_AI_USAGE_INPUT_TOKEN_DETAILS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_USAGE_OUTPUT_TOKENS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_USAGE_OUTPUT_TOKENS", "name": "GEN_AI_USAGE_OUTPUT_TOKENS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS", "name": "GEN_AI_USAGE_OUTPUT_TOKEN_DETAILS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_USAGE_TOTAL_TOKENS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_USAGE_TOTAL_TOKENS", "name": "GEN_AI_USAGE_TOTAL_TOKENS", "setter_type": null, "type": "builtins.str"}}, "GEN_AI_USER_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.GEN_AI_USER_MESSAGE", "name": "GEN_AI_USER_MESSAGE", "setter_type": null, "type": "builtins.str"}}, "HAS_OTEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.HAS_OTEL", "name": "HAS_OTEL", "setter_type": null, "type": "builtins.bool"}}, "LANGSMITH_DOTTED_ORDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_DOTTED_ORDER", "name": "LANGSMITH_DOTTED_ORDER", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_METADATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_METADATA", "name": "LANGSMITH_METADATA", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_NAME", "name": "LANGSMITH_NAME", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_PARENT_RUN_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_PARENT_RUN_ID", "name": "LANGSMITH_PARENT_RUN_ID", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_REQUEST_HEADERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_REQUEST_HEADERS", "name": "LANGSMITH_REQUEST_HEADERS", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_REQUEST_STREAMING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_REQUEST_STREAMING", "name": "LANGSMITH_REQUEST_STREAMING", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_RUNTIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_RUNTIME", "name": "LANGSMITH_RUNTIME", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_RUN_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_RUN_ID", "name": "LANGSMITH_RUN_ID", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_RUN_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_RUN_TYPE", "name": "LANGSMITH_RUN_TYPE", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_SESSION_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_SESSION_ID", "name": "LANGSMITH_SESSION_ID", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_SESSION_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_SESSION_NAME", "name": "LANGSMITH_SESSION_NAME", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_TAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_TAGS", "name": "LANGSMITH_TAGS", "setter_type": null, "type": "builtins.str"}}, "LANGSMITH_TRACE_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.LANGSMITH_TRACE_ID", "name": "LANGSMITH_TRACE_ID", "setter_type": null, "type": "builtins.str"}}, "NonRecordingSpan": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.NonRecordingSpan", "kind": "Gdef"}, "OTELExporter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter", "name": "OTELExporter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langsmith._internal.otel._otel_exporter", "mro": ["langsmith._internal.otel._otel_exporter.OTELExporter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "tracer_provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_as_utc_nano": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._as_utc_nano", "name": "_as_utc_nano", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timestamp"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_as_utc_nano of OTELExporter", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_span_for_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "op", "run_info", "otel_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._create_span_for_run", "name": "_create_span_for_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "op", "run_info", "otel_context"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "langsmith._internal._operations.SerializedRunOperation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_span_for_run of OTELExporter", "ret_type": {".class": "UnionType", "items": ["opentelemetry.trace.span.Span", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deserialize_run_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._deserialize_run_info", "name": "_deserialize_run_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "op"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "langsmith._internal._operations.SerializedRunOperation"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_deserialize_run_info of OTELExporter", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_model_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._extract_model_name", "name": "_extract_model_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_info"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_model_name of OTELExporter", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_unified_run_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._extract_unified_run_tokens", "name": "_extract_unified_run_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "outputs"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_extract_unified_run_tokens of OTELExporter", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_gen_ai_system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "span", "run_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._set_gen_ai_system", "name": "_set_gen_ai_system", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "span", "run_info"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "opentelemetry.trace.span.Span", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_gen_ai_system of OTELExporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_invocation_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "span", "run_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._set_invocation_parameters", "name": "_set_invocation_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "span", "run_info"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "opentelemetry.trace.span.Span", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_invocation_parameters of OTELExporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_io_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "span", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._set_io_attributes", "name": "_set_io_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "span", "op"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "opentelemetry.trace.span.Span", "langsmith._internal._operations.SerializedRunOperation"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_io_attributes of OTELExporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_span_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "span", "run_info", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._set_span_attributes", "name": "_set_span_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "span", "run_info", "op"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "opentelemetry.trace.span.Span", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "langsmith._internal._operations.SerializedRunOperation"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_span_attributes of OTELExporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_spans": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._spans", "name": "_spans", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tracer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._tracer", "name": "_tracer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_update_span_for_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "op", "run_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter._update_span_for_run", "name": "_update_span_for_run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "op", "run_info"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", "langsmith._internal._operations.SerializedRunOperation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update_span_for_run of OTELExporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operations", "otel_context_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter.export_batch", "name": "export_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "operations", "otel_context_map"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", {".class": "Instance", "args": ["langsmith._internal._operations.SerializedRunOperation"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["uuid.UUID", {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "export_batch of OTELExporter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unified_run_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter.get_unified_run_tokens", "name": "get_unified_run_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "outputs"], "arg_types": ["langsmith._internal.otel._otel_exporter.OTELExporter", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_unified_run_tokens of OTELExporter", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langsmith._internal.otel._otel_exporter.OTELExporter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langsmith._internal.otel._otel_exporter.OTELExporter", "values": [], "variance": 0}, "slots": ["_spans", "_tracer"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SerializedRunOperation": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.SerializedRunOperation", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.Span", "kind": "Gdef"}, "SpanContext": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.SpanContext", "kind": "Gdef"}, "TraceFlags": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.TraceFlags", "kind": "Gdef"}, "TraceState": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.span.TraceState", "kind": "Gdef"}, "WELL_KNOWN_OPERATION_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.WELL_KNOWN_OPERATION_NAMES", "name": "WELL_KNOWN_OPERATION_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_exporter.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_exporter.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_exporter.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_exporter.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_exporter.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal.otel._otel_exporter.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_operation_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["run_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal.otel._otel_exporter._get_operation_name", "name": "_get_operation_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["run_type"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_operation_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_orjson": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._or<PERSON>son", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "get_otel_span_id_from_uuid": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._otel_utils.get_otel_span_id_from_uuid", "kind": "Gdef"}, "get_otel_trace_id_from_uuid": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._otel_utils.get_otel_trace_id_from_uuid", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith._internal.otel._otel_exporter.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ls_utils": {".class": "SymbolTableNode", "cross_ref": "langsmith.utils", "kind": "Gdef"}, "set_span_in_context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace.propagation.set_span_in_context", "kind": "Gdef"}, "trace": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.trace", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/otel/_otel_exporter.py"}