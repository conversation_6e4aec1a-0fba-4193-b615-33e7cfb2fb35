{"data_mtime": 1752086943, "dep_lines": [12, 13, 16, 25, 11, 12, 24, 3, 5, 6, 7, 8, 9, 11, 24, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 20, 5, 5, 10, 10, 10, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["langsmith._internal._or<PERSON>son", "langsmith._internal._operations", "langsmith._internal._otel_utils", "opentelemetry.context.context", "langsmith.utils", "langsmith._internal", "opentelemetry.trace", "__future__", "datetime", "logging", "uuid", "warnings", "typing", "langsmith", "opentelemetry", "builtins", "_frozen_importlib", "_warnings", "abc", "functools", "opentelemetry.context", "opentelemetry.trace.span"], "hash": "aebb277ba584458825084f9074c55eb653a11ebe", "id": "langsmith._internal.otel._otel_exporter", "ignore_all": true, "interface_hash": "be9ad4f78bb132ac9c62a01ddbe75b096137a972", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/otel/_otel_exporter.py", "plugin_data": null, "size": 27556, "suppressed": [], "version_id": "1.16.1"}