{"data_mtime": 1752086943, "dep_lines": [10, 18, 13, 17, 5, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["opentelemetry.exporter.otlp.proto.http.trace_exporter", "opentelemetry.sdk.trace.export", "opentelemetry.sdk.resources", "opentelemetry.sdk.trace", "langsmith.utils", "os", "langsmith", "builtins", "_frozen_importlib", "abc", "functools", "opentelemetry", "opentelemetry.sdk", "opentelemetry.trace", "typing"], "hash": "70d5d9cd986027a28623c24d61d0d6f43a7cfbd0", "id": "langsmith._internal.otel._otel_client", "ignore_all": true, "interface_hash": "934ac99cdd9f571eb80d3fd02f4b93052a401bec", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/otel/_otel_client.py", "plugin_data": null, "size": 2715, "suppressed": [], "version_id": "1.16.1"}