{".class": "MypyFile", "_fullname": "langsmith._internal._background_thread", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Client": {".class": "SymbolTableNode", "cross_ref": "langsmith.client.Client", "kind": "Gdef"}, "CompressedTraces": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._compressed_traces.CompressedTraces", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "opentelemetry.context.context.Context", "kind": "Gdef"}, "Empty": {".class": "SymbolTableNode", "cross_ref": "_queue.Empty", "kind": "Gdef"}, "HTTP_REQUEST_THREAD_POOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith._internal._background_thread.HTTP_REQUEST_THREAD_POOL", "name": "HTTP_REQUEST_THREAD_POOL", "setter_type": null, "type": "concurrent.futures.thread.ThreadPoolExecutor"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Queue": {".class": "SymbolTableNode", "cross_ref": "queue.Queue", "kind": "Gdef"}, "SerializedFeedbackOperation": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.SerializedFeedbackOperation", "kind": "Gdef"}, "SerializedRunOperation": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.SerializedRunOperation", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracingQueueItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langsmith._internal._background_thread.TracingQueueItem", "name": "TracingQueueItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.TracingQueueItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langsmith._internal._background_thread", "mro": ["langsmith._internal._background_thread.TracingQueueItem", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langsmith._internal._background_thread.TracingQueueItem", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of TracingQueueItem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["langsmith._internal._background_thread.TracingQueueItem", "langsmith._internal._background_thread.TracingQueueItem"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ge__ of TracingQueueItem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["langsmith._internal._background_thread.TracingQueueItem", "langsmith._internal._background_thread.TracingQueueItem"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__gt__ of TracingQueueItem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "priority", "item", "otel_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "priority", "item", "otel_context"], "arg_types": ["langsmith._internal._background_thread.TracingQueueItem", "builtins.str", {".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TracingQueueItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["langsmith._internal._background_thread.TracingQueueItem", "langsmith._internal._background_thread.TracingQueueItem"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__le__ of TracingQueueItem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langsmith._internal._background_thread.TracingQueueItem", "langsmith._internal._background_thread.TracingQueueItem"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__lt__ of TracingQueueItem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.item", "name": "item", "setter_type": null, "type": {".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}}}, "otel_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.otel_context", "name": "otel_context", "setter_type": null, "type": {".class": "UnionType", "items": ["opentelemetry.context.context.Context", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._background_thread.TracingQueueItem.priority", "name": "priority", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langsmith._internal._background_thread.TracingQueueItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langsmith._internal._background_thread.TracingQueueItem", "values": [], "variance": 0}, "slots": ["item", "otel_context", "priority"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_AUTO_SCALE_DOWN_NEMPTY_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._AUTO_SCALE_DOWN_NEMPTY_TRIGGER", "kind": "Gdef"}, "_AUTO_SCALE_UP_NTHREADS_LIMIT": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._AUTO_SCALE_UP_NTHREADS_LIMIT", "kind": "Gdef"}, "_AUTO_SCALE_UP_QSIZE_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._AUTO_SCALE_UP_QSIZE_TRIGGER", "kind": "Gdef"}, "_BOUNDARY": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._constants._BOUNDARY", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._background_thread.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._background_thread.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._background_thread.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._background_thread.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._background_thread.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._background_thread.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_ensure_ingest_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._ensure_ingest_config", "name": "_ensure_ingest_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["info"], "arg_types": ["langsmith.schemas.LangSmithInfo"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ensure_ingest_config", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.BatchIngestConfig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hybrid_tracing_thread_handle_batch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["client", "tracing_queue", "batch", "use_multipart", "mark_task_done"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._hybrid_tracing_thread_handle_batch", "name": "_hybrid_tracing_thread_handle_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["client", "tracing_queue", "batch", "use_multipart", "mark_task_done"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "queue.Queue"}, {".class": "Instance", "args": ["langsmith._internal._background_thread.TracingQueueItem"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_hybrid_tracing_thread_handle_batch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_using_internal_otlp_provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._is_using_internal_otlp_provider", "name": "_is_using_internal_otlp_provider", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["client"], "arg_types": ["langsmith.client.Client"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_using_internal_otlp_provider", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_otel_tracing_thread_handle_batch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["client", "tracing_queue", "batch", "mark_task_done", "ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._otel_tracing_thread_handle_batch", "name": "_otel_tracing_thread_handle_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["client", "tracing_queue", "batch", "mark_task_done", "ops"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "queue.Queue"}, {".class": "Instance", "args": ["langsmith._internal._background_thread.TracingQueueItem"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_otel_tracing_thread_handle_batch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tracing_sub_thread_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["client_ref", "use_multipart"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._tracing_sub_thread_func", "name": "_tracing_sub_thread_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["client_ref", "use_multipart"], "arg_types": [{".class": "Instance", "args": ["langsmith.client.Client"], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_tracing_sub_thread_func", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tracing_thread_drain_compressed_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["client", "size_limit", "size_limit_bytes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._tracing_thread_drain_compressed_buffer", "name": "_tracing_thread_drain_compressed_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["client", "size_limit", "size_limit_bytes"], "arg_types": ["langsmith.client.Client", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_tracing_thread_drain_compressed_buffer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["_io.BytesIO", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tracing_thread_drain_queue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["tracing_queue", "limit", "block"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._tracing_thread_drain_queue", "name": "_tracing_thread_drain_queue", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["tracing_queue", "limit", "block"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "queue.Queue"}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_tracing_thread_drain_queue", "ret_type": {".class": "Instance", "args": ["langsmith._internal._background_thread.TracingQueueItem"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tracing_thread_handle_batch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["client", "tracing_queue", "batch", "use_multipart", "mark_task_done", "ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread._tracing_thread_handle_batch", "name": "_tracing_thread_handle_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["client", "tracing_queue", "batch", "use_multipart", "mark_task_done", "ops"], "arg_types": ["langsmith.client.Client", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "queue.Queue"}, {".class": "Instance", "args": ["langsmith._internal._background_thread.TracingQueueItem"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_tracing_thread_handle_batch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "cf": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures", "kind": "Gdef"}, "combine_serialized_queue_operations": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._operations.combine_serialized_queue_operations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "cpu_count": {".class": "SymbolTableNode", "cross_ref": "multiprocessing.cpu_count", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_size_limit_from_env": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.get_size_limit_from_env", "name": "get_size_limit_from_env", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_size_limit_from_env", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tracing_mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.get_tracing_mode", "name": "get_tracing_mode", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_tracing_mode", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith._internal._background_thread.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ls_schemas": {".class": "SymbolTableNode", "cross_ref": "langsmith.schemas", "kind": "Gdef"}, "ls_utils": {".class": "SymbolTableNode", "cross_ref": "langsmith.utils", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "tracing_control_thread_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["client_ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.tracing_control_thread_func", "name": "tracing_control_thread_func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["client_ref"], "arg_types": [{".class": "Instance", "args": ["langsmith.client.Client"], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tracing_control_thread_func", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tracing_control_thread_func_compress_parallel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["client_ref", "flush_interval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._background_thread.tracing_control_thread_func_compress_parallel", "name": "tracing_control_thread_func_compress_parallel", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["client_ref", "flush_interval"], "arg_types": [{".class": "Instance", "args": ["langsmith.client.Client"], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}, "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tracing_control_thread_func_compress_parallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/_background_thread.py"}