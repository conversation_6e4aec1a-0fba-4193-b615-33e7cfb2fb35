{"data_mtime": 1752086943, "dep_lines": [12, 13, 14, 15, 7, 11, 12, 1, 3, 4, 5, 6, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 20, 5, 10, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langsmith._internal._or<PERSON>son", "langsmith._internal._compressed_traces", "langsmith._internal._multipart", "langsmith._internal._serde", "collections.abc", "langsmith.schemas", "langsmith._internal", "__future__", "itertools", "logging", "os", "uuid", "io", "typing", "langsmith", "builtins", "_frozen_importlib", "_io", "abc", "pathlib", "pydantic", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils"], "hash": "62e75bbf03af77d367e159d396b3c112c3f96511", "id": "langsmith._internal._operations", "ignore_all": true, "interface_hash": "6d1f540f1b85f4d394da18601f73a8fe183d4bdc", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/_operations.py", "plugin_data": null, "size": 11416, "suppressed": [], "version_id": "1.16.1"}