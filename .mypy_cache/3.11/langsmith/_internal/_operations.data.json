{".class": "MypyFile", "_fullname": "langsmith._internal._operations", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BufferedReader": {".class": "SymbolTableNode", "cross_ref": "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "CompressedTraces": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._compressed_traces.CompressedTraces", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MultipartPart": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._multipart.MultipartPart", "kind": "Gdef"}, "MultipartPartsAndContext": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._multipart.MultipartPartsAndContext", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SerializedFeedbackOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langsmith._internal._operations.SerializedFeedbackOperation", "name": "SerializedFeedbackOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langsmith._internal._operations", "mro": ["langsmith._internal._operations.SerializedFeedbackOperation", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langsmith._internal._operations.SerializedFeedbackOperation", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of SerializedFeedbackOperation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "id", "trace_id", "feedback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "id", "trace_id", "feedback"], "arg_types": ["langsmith._internal._operations.SerializedFeedbackOperation", "uuid.UUID", "uuid.UUID", "builtins.bytes"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SerializedFeedbackOperation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.feedback", "name": "feedback", "setter_type": null, "type": "builtins.bytes"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.id", "name": "id", "setter_type": null, "type": "uuid.UUID"}}, "trace_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.trace_id", "name": "trace_id", "setter_type": null, "type": "uuid.UUID"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langsmith._internal._operations.SerializedFeedbackOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langsmith._internal._operations.SerializedFeedbackOperation", "values": [], "variance": 0}, "slots": ["feedback", "id", "trace_id"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SerializedRunOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langsmith._internal._operations.SerializedRunOperation", "name": "SerializedRunOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.SerializedRunOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langsmith._internal._operations", "mro": ["langsmith._internal._operations.SerializedRunOperation", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._operations.SerializedRunOperation.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langsmith._internal._operations.SerializedRunOperation", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of SerializedRunOperation", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "operation", "id", "trace_id", "_none", "inputs", "outputs", "events", "attachments"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langsmith._internal._operations.SerializedRunOperation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "operation", "id", "trace_id", "_none", "inputs", "outputs", "events", "attachments"], "arg_types": ["langsmith._internal._operations.SerializedRunOperation", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "post"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "patch"}], "uses_pep604_syntax": false}, "uuid.UUID", "uuid.UUID", "builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.Attachments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SerializedRunOperation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "langsmith._internal._operations.SerializedRunOperation.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation._none", "name": "_none", "setter_type": null, "type": "builtins.bytes"}}, "attachments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.attachments", "name": "attachments", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langsmith.schemas.Attachments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.events", "name": "events", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.id", "name": "id", "setter_type": null, "type": "uuid.UUID"}}, "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.inputs", "name": "inputs", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.operation", "name": "operation", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "post"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "patch"}], "uses_pep604_syntax": false}}}, "outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.outputs", "name": "outputs", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "trace_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langsmith._internal._operations.SerializedRunOperation.trace_id", "name": "trace_id", "setter_type": null, "type": "uuid.UUID"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langsmith._internal._operations.SerializedRunOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langsmith._internal._operations.SerializedRunOperation", "values": [], "variance": 0}, "slots": ["_none", "attachments", "events", "id", "inputs", "operation", "outputs", "trace_id"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._operations.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._operations.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._operations.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._operations.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._operations.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langsmith._internal._operations.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_dumps_json": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._serde.dumps_json", "kind": "Gdef"}, "_orjson": {".class": "SymbolTableNode", "cross_ref": "langsmith._internal._or<PERSON>son", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "combine_serialized_queue_operations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.combine_serialized_queue_operations", "name": "combine_serialized_queue_operations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ops"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "combine_serialized_queue_operations", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["langsmith._internal._operations.SerializedRunOperation", "langsmith._internal._operations.SerializedFeedbackOperation"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compress_multipart_parts_and_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["parts_and_context", "compressed_traces", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.compress_multipart_parts_and_context", "name": "compress_multipart_parts_and_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["parts_and_context", "compressed_traces", "boundary"], "arg_types": ["langsmith._internal._multipart.MultipartPartsAndContext", "langsmith._internal._compressed_traces.CompressedTraces", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compress_multipart_parts_and_context", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_multipart_parts_and_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["parts_and_context", "boundary"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.encode_multipart_parts_and_context", "name": "encode_multipart_parts_and_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["parts_and_context", "boundary"], "arg_types": ["langsmith._internal._multipart.MultipartPartsAndContext", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encode_multipart_parts_and_context", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langsmith._internal._operations.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ls_schemas": {".class": "SymbolTableNode", "cross_ref": "langsmith.schemas", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "serialize_feedback_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["feedback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.serialize_feedback_dict", "name": "serialize_feedback_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["feedback"], "arg_types": [{".class": "UnionType", "items": ["langsmith.schemas.FeedbackCreate", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialize_feedback_dict", "ret_type": "langsmith._internal._operations.SerializedFeedbackOperation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serialize_run_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["operation", "payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.serialize_run_dict", "name": "serialize_run_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["operation", "payload"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "post"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "patch"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialize_run_dict", "ret_type": "langsmith._internal._operations.SerializedRunOperation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serialized_feedback_operation_to_multipart_parts_and_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.serialized_feedback_operation_to_multipart_parts_and_context", "name": "serialized_feedback_operation_to_multipart_parts_and_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": ["langsmith._internal._operations.SerializedFeedbackOperation"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialized_feedback_operation_to_multipart_parts_and_context", "ret_type": "langsmith._internal._multipart.MultipartPartsAndContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serialized_run_operation_to_multipart_parts_and_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langsmith._internal._operations.serialized_run_operation_to_multipart_parts_and_context", "name": "serialized_run_operation_to_multipart_parts_and_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": ["langsmith._internal._operations.SerializedRunOperation"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "serialized_run_operation_to_multipart_parts_and_context", "ret_type": {".class": "TupleType", "implicit": false, "items": ["langsmith._internal._multipart.MultipartPartsAndContext", {".class": "Instance", "args": ["builtins.str", "_io.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/_operations.py"}