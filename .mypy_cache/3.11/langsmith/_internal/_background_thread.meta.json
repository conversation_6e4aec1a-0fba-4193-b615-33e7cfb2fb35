{"data_mtime": 1752086943, "dep_lines": [23, 24, 30, 37, 3, 21, 22, 39, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 10, 10, 10, 25, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langsmith._internal._compressed_traces", "langsmith._internal._constants", "langsmith._internal._operations", "opentelemetry.context.context", "concurrent.futures", "langsmith.schemas", "langsmith.utils", "langsmith.client", "__future__", "concurrent", "copy", "functools", "io", "logging", "sys", "threading", "time", "weakref", "multiprocessing", "queue", "typing", "langsmith", "builtins", "_frozen_importlib", "_io", "abc", "concurrent.futures._base", "concurrent.futures.thread", "multiprocessing.context", "opentelemetry", "opentelemetry.context", "pydantic", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils"], "hash": "6b86a8c5f3dc9b3b7edce02cd57ff50203a8fc0a", "id": "langsmith._internal._background_thread", "ignore_all": true, "interface_hash": "3b2018ccc5fbf52c1e997e1436efbe4e469fbd1f", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/_background_thread.py", "plugin_data": null, "size": 29933, "suppressed": [], "version_id": "1.16.1"}