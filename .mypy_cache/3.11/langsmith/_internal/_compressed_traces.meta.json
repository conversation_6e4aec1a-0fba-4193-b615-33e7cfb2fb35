{"data_mtime": 1752086943, "dep_lines": [6, 1, 2, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langsmith.utils", "io", "threading", "zstandard", "langsmith", "builtins", "_frozen_importlib", "_io", "_thread", "abc", "functools", "typing", "typing_extensions"], "hash": "ab2f8091d64d4662ae51ac871c55617a9c5528a8", "id": "langsmith._internal._compressed_traces", "ignore_all": true, "interface_hash": "42e125d2fbadd4065c3f614d71a3b7bcef70ef24", "mtime": 1751599584, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langsmith/_internal/_compressed_traces.py", "plugin_data": null, "size": 860, "suppressed": [], "version_id": "1.16.1"}