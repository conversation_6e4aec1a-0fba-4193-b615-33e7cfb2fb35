{"data_mtime": 1752086944, "dep_lines": [14, 15, 16, 20, 21, 22, 32, 41, 49, 52, 50, 52, 4, 5, 6, 7, 8, 9, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard._utils", "torch.distributed.checkpoint._dedup_save_plans", "torch.distributed.checkpoint._nested_dict", "torch.distributed.checkpoint._sharded_tensor_utils", "torch.distributed.checkpoint._traverse", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.planner_helpers", "torch.distributed.checkpoint.utils", "torch.distributed.checkpoint._version", "torch.distributed.tensor", "torch.distributed.checkpoint", "copy", "dataclasses", "io", "logging", "operator", "collections", "functools", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_io", "abc", "torch._C", "torch._tensor", "types"], "hash": "74223ffbfedcd0d781fa7c273fc98c8afb2b5f94", "id": "torch.distributed.checkpoint.default_planner", "ignore_all": true, "interface_hash": "0576f50e592950400ba96a9e230087b5bc8afedd", "mtime": 1751599561, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/torch/distributed/checkpoint/default_planner.py", "plugin_data": null, "size": 24831, "suppressed": [], "version_id": "1.16.1"}