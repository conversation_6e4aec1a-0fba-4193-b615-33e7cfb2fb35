{"data_mtime": 1752086944, "dep_lines": [3, 5, 6, 5, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint._version", "torch.distributed.checkpoint._traverse", "torch.distributed.checkpoint", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "c81669e7d9cfa2f8c3cac9bcfb03e098a3c88f20", "id": "torch.distributed.checkpoint._nested_dict", "ignore_all": true, "interface_hash": "6b8a5417d54d340b0698bc98f7375e84626ec99a", "mtime": 1751599561, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/torch/distributed/checkpoint/_nested_dict.py", "plugin_data": null, "size": 2273, "suppressed": [], "version_id": "1.16.1"}