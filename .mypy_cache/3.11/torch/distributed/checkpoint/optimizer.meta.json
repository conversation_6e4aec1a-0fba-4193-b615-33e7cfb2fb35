{"data_mtime": 1752086944, "dep_lines": [10, 11, 14, 15, 16, 17, 18, 27, 28, 32, 33, 34, 40, 39, 41, 42, 4, 8, 9, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard.sharded_tensor.api", "torch.distributed._shard.sharded_tensor.metadata", "torch.distributed._shard.sharded_tensor.shard", "torch.distributed._shard.sharding_spec.chunk_sharding_spec", "torch.distributed.checkpoint._nested_dict", "torch.distributed.checkpoint.default_planner", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.planner_helpers", "torch.distributed.checkpoint.state_dict_loader", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "torch.distributed.fsdp._shard_utils", "torch.distributed.distributed_c10d", "torch.distributed.remote_device", "torch.distributed.tensor", "collections.abc", "torch.distributed", "torch._utils", "dataclasses", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.distributed._shard", "torch.distributed._shard.metadata", "torch.distributed._shard.sharded_tensor", "torch.distributed._shard.sharding_spec", "torch.distributed._shard.sharding_spec.api", "types"], "hash": "3c97bfe964fbad7dff0e142bfd6b95764f19319c", "id": "torch.distributed.checkpoint.optimizer", "ignore_all": true, "interface_hash": "4bd2cb40d15f43a0df698699cf2270521e536dd8", "mtime": 1751599561, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/torch/distributed/checkpoint/optimizer.py", "plugin_data": null, "size": 13154, "suppressed": [], "version_id": "1.16.1"}