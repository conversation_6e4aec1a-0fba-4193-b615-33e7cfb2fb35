{"data_mtime": 1752086944, "dep_lines": [12, 5, 6, 7, 8, 1, 3, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain.output_parsers.prompts", "langchain_core.exceptions", "langchain_core.output_parsers", "langchain_core.prompts", "langchain_core.runnables", "__future__", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "langchain_core", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.output_parsers.base", "langchain_core.prompts.base", "langchain_core.prompts.prompt", "langchain_core.prompts.string", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "ca3c1503935acb131e6a7542057bea1543e920a7", "id": "langchain.output_parsers.fix", "ignore_all": true, "interface_hash": "434f5d699987a3c691c884eb517f0348b5a35e1b", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain/output_parsers/fix.py", "plugin_data": null, "size": 5590, "suppressed": [], "version_id": "1.16.1"}