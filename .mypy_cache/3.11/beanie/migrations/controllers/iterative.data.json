{".class": "MypyFile", "_fullname": "beanie.migrations.controllers.iterative", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseMigrationController": {".class": "SymbolTableNode", "cross_ref": "beanie.migrations.controllers.base.BaseMigrationController", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.Document", "kind": "Gdef"}, "DummyOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.migrations.controllers.iterative.DummyOutput", "name": "DummyOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.migrations.controllers.iterative.DummyOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.migrations.controllers.iterative", "mro": ["beanie.migrations.controllers.iterative.DummyOutput", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.migrations.controllers.iterative.DummyOutput.__getattr__", "name": "__getattr__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.migrations.controllers.iterative.DummyOutput.__init__", "name": "__init__", "type": null}}, "__setattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.migrations.controllers.iterative.DummyOutput.__setattr__", "name": "__setattr__", "type": null}}, "dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "to_parse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.migrations.controllers.iterative.DummyOutput.dict", "name": "dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "to_parse"], "arg_types": ["beanie.migrations.controllers.iterative.DummyOutput", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "beanie.migrations.controllers.iterative.DummyOutput", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dict of DummyOutput", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.migrations.controllers.iterative.DummyOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.migrations.controllers.iterative.DummyOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IS_PYDANTIC_V2": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.IS_PYDANTIC_V2", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.migrations.controllers.iterative.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.migrations.controllers.iterative.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.migrations.controllers.iterative.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.migrations.controllers.iterative.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.migrations.controllers.iterative.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.migrations.controllers.iterative.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "isclass": {".class": "SymbolTableNode", "cross_ref": "inspect.isclass", "kind": "Gdef"}, "iterative_migration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["document_models", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.migrations.controllers.iterative.iterative_migration", "name": "iterative_migration", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["document_models", "batch_size"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iterative_migration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_model": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.parse_model", "kind": "Gdef"}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "update_dict": {".class": "SymbolTableNode", "cross_ref": "beanie.migrations.utils.update_dict", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/migrations/controllers/iterative.py"}