{".class": "MypyFile", "_fullname": "beanie", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "After": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.After", "kind": "Gdef"}, "BackLink": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.BackLink", "kind": "Gdef"}, "BeanieObjectId": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.BeanieObjectId", "kind": "Gdef"}, "Before": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.Before", "kind": "Gdef"}, "BsonBinary": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.custom_types.bson.binary.BsonBinary", "kind": "Gdef"}, "BulkWriter": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.bulk.BulkWriter", "kind": "Gdef"}, "DecimalAnnotation": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.custom_types.decimal.DecimalAnnotation", "kind": "Gdef"}, "Delete": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.Delete", "kind": "Gdef"}, "DeleteRules": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.DeleteRules", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.Document", "kind": "Gdef"}, "DocumentWithSoftDelete": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.DocumentWithSoftDelete", "kind": "Gdef"}, "Granularity": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.settings.timeseries.Granularity", "kind": "Gdef"}, "Indexed": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.Indexed", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.Insert", "kind": "Gdef"}, "Link": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.Link", "kind": "Gdef"}, "MergeStrategy": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.MergeStrategy", "kind": "Gdef"}, "PydanticObjectId": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.PydanticObjectId", "kind": "Gdef"}, "Replace": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.Replace", "kind": "Gdef"}, "Save": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.Save", "kind": "Gdef"}, "SaveChanges": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.SaveChanges", "kind": "Gdef"}, "SortDirection": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.enums.SortDirection", "kind": "Gdef"}, "TimeSeriesConfig": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.settings.timeseries.TimeSeriesConfig", "kind": "Gdef"}, "UnionDoc": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.union_doc.UnionDoc", "kind": "Gdef"}, "Update": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.Update", "kind": "Gdef"}, "UpdateResponse": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateResponse", "kind": "Gdef"}, "ValidateOnSave": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.ValidateOnSave", "kind": "Gdef"}, "View": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.views.View", "kind": "Gdef"}, "WriteRules": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.WriteRules", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "beanie.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "after_event": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.after_event", "kind": "Gdef"}, "before_event": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.before_event", "kind": "Gdef"}, "free_fall_migration": {".class": "SymbolTableNode", "cross_ref": "beanie.migrations.controllers.free_fall.free_fall_migration", "kind": "Gdef"}, "init_beanie": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.init.init_beanie", "kind": "Gdef"}, "iterative_migration": {".class": "SymbolTableNode", "cross_ref": "beanie.migrations.controllers.iterative.iterative_migration", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/__init__.py"}