{"data_mtime": 1752086944, "dep_lines": [1, 2, 8, 18, 19, 27, 35, 36, 43, 44, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["beanie.odm.operators.find.array", "beanie.odm.operators.find.bitwise", "beanie.odm.operators.find.comparison", "beanie.odm.operators.find.element", "beanie.odm.operators.find.evaluation", "beanie.odm.operators.find.geospatial", "beanie.odm.operators.find.logical", "beanie.odm.operators.update.array", "beanie.odm.operators.update.bitwise", "beanie.odm.operators.update.general", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "f9a0104b930a8244fa67f004ea8c4dabec324db8", "id": "beanie.operators", "ignore_all": true, "interface_hash": "4ae64e0b0c5b74183c2bebc9be4be951e877a668", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/operators.py", "plugin_data": null, "size": 1845, "suppressed": [], "version_id": "1.16.1"}