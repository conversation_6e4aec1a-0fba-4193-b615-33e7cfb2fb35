{"data_mtime": 1752086944, "dep_lines": [28, 38, 39, 27, 37, 60, 22, 52, 53, 56, 57, 1, 3, 4, 5, 6, 7, 21, 23, 24, 25, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["beanie.odm.operators.find.comparison", "beanie.odm.utils.parsing", "beanie.odm.utils.pydantic", "beanie.odm.enums", "beanie.odm.registry", "beanie.odm.documents", "bson.errors", "pydantic.json_schema", "pydantic_core.core_schema", "pydantic.fields", "pydantic.json", "__future__", "asyncio", "collections", "dataclasses", "enum", "typing", "bson", "pydantic", "pymongo", "typing_extensions", "pydantic_core", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "beanie.odm.interfaces", "beanie.odm.interfaces.aggregate", "beanie.odm.interfaces.find", "beanie.odm.interfaces.getters", "beanie.odm.interfaces.inheritance", "beanie.odm.interfaces.setters", "bson.dbref", "bson.objectid", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.annotated_handlers", "pydantic.main", "pymongo.operations"], "hash": "06117b3cd33c388b6a9a8dc4387933156d96f7cd", "id": "beanie.odm.fields", "ignore_all": true, "interface_hash": "026509874a26612663552b48854795f18dac56b6", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/fields.py", "plugin_data": null, "size": 22227, "suppressed": [], "version_id": "1.16.1"}