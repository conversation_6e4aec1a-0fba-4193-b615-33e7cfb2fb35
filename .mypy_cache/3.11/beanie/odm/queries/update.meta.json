{"data_mtime": 1752086944, "dep_lines": [29, 23, 24, 25, 28, 30, 31, 22, 34, 16, 20, 1, 2, 3, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["beanie.odm.operators.update.general", "beanie.odm.interfaces.clone", "beanie.odm.interfaces.session", "beanie.odm.interfaces.update", "beanie.odm.operators.update", "beanie.odm.utils.encoder", "beanie.odm.utils.parsing", "beanie.odm.bulk", "beanie.odm.documents", "motor.motor_asyncio", "pymongo.results", "abc", "enum", "typing", "pymongo", "builtins", "_frozen_importlib", "_typeshed", "beanie.odm.interfaces", "beanie.odm.interfaces.aggregate", "beanie.odm.interfaces.find", "beanie.odm.interfaces.getters", "beanie.odm.interfaces.inheritance", "beanie.odm.interfaces.setters", "beanie.odm.settings", "beanie.odm.settings.base", "beanie.odm.settings.document", "motor", "motor.core", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types", "typing_extensions"], "hash": "42bc27da155a43ea64114bf5238152f14efa3775", "id": "beanie.odm.queries.update", "ignore_all": true, "interface_hash": "a057a63acd6224f5e7474ad52aba8a127cf5b666", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/queries/update.py", "plugin_data": null, "size": 12567, "suppressed": [], "version_id": "1.16.1"}