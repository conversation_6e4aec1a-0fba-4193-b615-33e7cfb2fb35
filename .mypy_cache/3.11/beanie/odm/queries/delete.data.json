{".class": "MypyFile", "_fullname": "beanie.odm.queries.delete", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BulkWriter": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.bulk.BulkWriter", "kind": "Gdef"}, "CloneInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.clone.CloneInterface", "kind": "Gdef"}, "DeleteMany": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.queries.delete.DeleteQuery"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.delete.DeleteMany", "name": "DeleteMany", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.delete.DeleteMany", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.delete", "mro": ["beanie.odm.queries.delete.DeleteMany", "beanie.odm.queries.delete.DeleteQuery", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.delete.DeleteMany.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.delete.DeleteMany"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__await__ of DeleteMany", "ret_type": {".class": "Instance", "args": ["pymongo.results.DeleteResult", {".class": "NoneType"}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.delete.DeleteMany.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.delete.DeleteMany", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeleteManyPyMongo": {".class": "SymbolTableNode", "cross_ref": "pymongo.operations.DeleteMany", "kind": "Gdef"}, "DeleteOne": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.queries.delete.DeleteQuery"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.delete.DeleteOne", "name": "DeleteOne", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.delete.DeleteOne", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.delete", "mro": ["beanie.odm.queries.delete.DeleteOne", "beanie.odm.queries.delete.DeleteQuery", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.delete.DeleteOne.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.delete.DeleteOne"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__await__ of DeleteOne", "ret_type": {".class": "Instance", "args": ["pymongo.results.DeleteResult", {".class": "NoneType"}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.delete.DeleteOne.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.delete.DeleteOne", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeleteOnePyMongo": {".class": "SymbolTableNode", "cross_ref": "pymongo.operations.DeleteOne", "kind": "Gdef"}, "DeleteQuery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.delete.DeleteQuery", "name": "<PERSON>ete<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.delete.DeleteQuery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.delete", "mro": ["beanie.odm.queries.delete.DeleteQuery", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "document_model", "find_query", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.delete.DeleteQuery.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "document_model", "find_query", "bulk_writer", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.delete.DeleteQuery", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.delete.DeleteQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DeleteQuery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.delete.DeleteQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "bulk_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.delete.DeleteQuery.bulk_writer", "name": "bulk_writer", "setter_type": null, "type": {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "document_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.delete.DeleteQuery.document_model", "name": "document_model", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.delete.DeleteQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}}}, "find_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.delete.DeleteQuery.find_query", "name": "find_query", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "pymongo_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.delete.DeleteQuery.pymongo_kwargs", "name": "pymongo_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.delete.DeleteQuery.session", "name": "session", "setter_type": null, "type": {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.delete.DeleteQuery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.delete.DeleteQuery", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeleteResult": {".class": "SymbolTableNode", "cross_ref": "pymongo.results.DeleteResult", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.DocType", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SessionMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.session.SessionMethods", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.delete.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.delete.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.delete.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.delete.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.delete.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.delete.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/queries/delete.py"}