{".class": "MypyFile", "_fullname": "beanie.odm.queries.aggregation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregationProjectionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "name": "AggregationProjectionType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "AggregationQuery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.cursor.BaseCursorQuery"}, "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.aggregation.AggregationQuery", "name": "AggregationQuery", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.aggregation.AggregationQuery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.aggregation", "mro": ["beanie.odm.queries.aggregation.AggregationQuery", "beanie.odm.queries.cursor.BaseCursorQuery", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "document_model", "aggregation_pipeline", "find_query", "projection_model", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "document_model", "aggregation_pipeline", "find_query", "projection_model", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AggregationQuery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery._cache_key", "name": "_cache_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_cache_key of AggregationQuery", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery._cache_key", "name": "_cache_key", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_cache_key of AggregationQuery", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.aggregation.AggregationQuery._get_cache", "name": "_get_cache", "type": null}}, "_set_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.aggregation.AggregationQuery._set_cache", "name": "_set_cache", "type": null}}, "aggregation_pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.aggregation_pipeline", "name": "aggregation_pipeline", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "document_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.document_model", "name": "document_model", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}}}, "find_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.find_query", "name": "find_query", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "get_aggregation_pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.get_aggregation_pipeline", "name": "get_aggregation_pipeline", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_aggregation_pipeline of AggregationQuery", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_projection_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.get_projection_model", "name": "get_projection_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_projection_model of AggregationQuery", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.ignore_cache", "name": "ignore_cache", "setter_type": null, "type": "builtins.bool"}}, "motor_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.motor_cursor", "name": "motor_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "motor_cursor of AggregationQuery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "motor.core.AgnosticCommandCursor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.motor_cursor", "name": "motor_cursor", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "motor_cursor of AggregationQuery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "motor.core.AgnosticCommandCursor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "projection_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.projection_model", "name": "projection_model", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pymongo_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.aggregation.AggregationQuery.pymongo_kwargs", "name": "pymongo_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationQuery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["AggregationProjectionType"], "typeddict_type": null}}, "AgnosticCommandCursor": {".class": "SymbolTableNode", "cross_ref": "motor.core.AgnosticCommandCursor", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseCursorQuery": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.cursor.BaseCursorQuery", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "CloneInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.clone.CloneInterface", "kind": "Gdef"}, "DocType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.DocType", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "LRUCache": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.cache.LRUCache", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SessionMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.session.SessionMethods", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.aggregation.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.aggregation.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.aggregation.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.aggregation.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.aggregation.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.aggregation.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "get_projection": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.projection.get_projection", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/queries/aggregation.py"}