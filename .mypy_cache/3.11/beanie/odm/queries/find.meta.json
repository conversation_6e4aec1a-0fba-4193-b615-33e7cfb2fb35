{"data_mtime": 1752086944, "dep_lines": [33, 29, 30, 31, 32, 34, 35, 36, 40, 46, 47, 48, 49, 50, 51, 26, 27, 28, 54, 20, 23, 25, 1, 21, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["beanie.odm.operators.find.logical", "beanie.odm.interfaces.aggregation_methods", "beanie.odm.interfaces.clone", "beanie.odm.interfaces.session", "beanie.odm.interfaces.update", "beanie.odm.queries.aggregation", "beanie.odm.queries.cursor", "beanie.odm.queries.delete", "beanie.odm.queries.update", "beanie.odm.utils.dump", "beanie.odm.utils.encoder", "beanie.odm.utils.find", "beanie.odm.utils.parsing", "beanie.odm.utils.projection", "beanie.odm.utils.relations", "beanie.odm.bulk", "beanie.odm.cache", "beanie.odm.enums", "beanie.odm.documents", "motor.motor_asyncio", "pymongo.results", "beanie.exceptions", "typing", "pydantic", "pymongo", "builtins", "_frozen_importlib", "_typeshed", "abc", "beanie.odm.interfaces", "beanie.odm.interfaces.aggregate", "beanie.odm.interfaces.find", "beanie.odm.interfaces.getters", "beanie.odm.interfaces.inheritance", "beanie.odm.interfaces.setters", "enum", "motor", "motor.core", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "574337197c95f6015da73584245007ec78f4867e", "id": "beanie.odm.queries.find", "ignore_all": true, "interface_hash": "71e1f687141cfd4558e1281af7ee58887d681df8", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/queries/find.py", "plugin_data": null, "size": 36657, "suppressed": [], "version_id": "1.16.1"}