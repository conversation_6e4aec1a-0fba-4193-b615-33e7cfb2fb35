{".class": "MypyFile", "_fullname": "beanie.odm.queries.update", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BaseUpdateOperator": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.BaseUpdateOperator", "kind": "Gdef"}, "BulkWriter": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.bulk.BulkWriter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CloneInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.clone.CloneInterface", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.DocType", "kind": "Gdef"}, "Encoder": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.encoder.Encoder", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "InsertOneResult": {".class": "SymbolTableNode", "cross_ref": "pymongo.results.InsertOneResult", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ReturnDocument": {".class": "SymbolTableNode", "cross_ref": "pymongo.synchronous.collection.ReturnDocument", "kind": "Gdef"}, "SessionMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.session.SessionMethods", "kind": "Gdef"}, "SetRevisionId": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.general.SetRevisionId", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateMany": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.queries.update.UpdateQuery"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.update.UpdateMany", "name": "UpdateMany", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.update.UpdateMany", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.update", "mro": ["beanie.odm.queries.update.UpdateMany", "beanie.odm.queries.update.UpdateQuery", "beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateMany.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.update.UpdateMany"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__await__ of UpdateMany", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "UnionType", "items": ["pymongo.results.UpdateResult", "pymongo.results.InsertOneResult", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateMany.__await__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateMany.__await__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "beanie.odm.queries.update.UpdateMany._update", "name": "_update", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateMany.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateMany", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of UpdateMany", "ret_type": "beanie.odm.queries.update.UpdateQuery", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateMany.update_many", "name": "update_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateMany", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_many of UpdateMany", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateMany.upsert", "name": "upsert", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateMany", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateMany.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert of UpdateMany", "ret_type": "beanie.odm.queries.update.UpdateQuery", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateMany.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.update.UpdateMany.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.update.UpdateMany", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UpdateManyPyMongo": {".class": "SymbolTableNode", "cross_ref": "pymongo.operations.UpdateMany", "kind": "Gdef"}, "UpdateMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.update.UpdateMethods", "kind": "Gdef"}, "UpdateOne": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.queries.update.UpdateQuery"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.update.UpdateOne", "name": "UpdateOne", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.update.UpdateOne", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.update", "mro": ["beanie.odm.queries.update.UpdateOne", "beanie.odm.queries.update.UpdateQuery", "beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateOne.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.update.UpdateOne"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__await__ of UpdateOne", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "UnionType", "items": ["pymongo.results.UpdateResult", "pymongo.results.InsertOneResult", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateOne.__await__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateOne.__await__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateOne.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateOne", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UpdateOne", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "beanie.odm.queries.update.UpdateOne._update", "name": "_update", "type": null}}, "response_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.update.UpdateOne.response_type", "name": "response_type", "setter_type": null, "type": "beanie.odm.queries.update.UpdateResponse"}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateOne.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateOne", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.queries.update.UpdateResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of UpdateOne", "ret_type": "beanie.odm.queries.update.UpdateQuery", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateOne.update_one", "name": "update_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateOne", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.queries.update.UpdateResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_one of UpdateOne", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "response_type", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateOne.upsert", "name": "upsert", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "response_type", "pymongo_kwargs"], "arg_types": ["beanie.odm.queries.update.UpdateOne", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateOne.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.queries.update.UpdateResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert of UpdateOne", "ret_type": "beanie.odm.queries.update.UpdateQuery", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateOne.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.update.UpdateOne.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.update.UpdateOne", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UpdateOnePyMongo": {".class": "SymbolTableNode", "cross_ref": "pymongo.operations.UpdateOne", "kind": "Gdef"}, "UpdateQuery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_update", 1], ["update", 1]], "alt_promote": null, "bases": ["beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.update.UpdateQuery", "name": "Update<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "beanie.odm.queries.update.UpdateQuery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.update", "mro": ["beanie.odm.queries.update.UpdateQuery", "beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document_model", "find_query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateQuery.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document_model", "find_query"], "arg_types": ["beanie.odm.queries.update.UpdateQuery", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of UpdateQuery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateQuery._update", "name": "_update", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.update.UpdateQuery"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update of UpdateQuery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pymongo.results.UpdateResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery._update", "name": "_update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.update.UpdateQuery"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update of UpdateQuery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pymongo.results.UpdateResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bulk_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.bulk_writer", "name": "bulk_writer", "setter_type": null, "type": {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "document_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.document_model", "name": "document_model", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}}}, "encoders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.encoders", "name": "encoders", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "find_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.find_query", "name": "find_query", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "is_upsert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.is_upsert", "name": "is_upsert", "setter_type": null, "type": "builtins.bool"}}, "pymongo_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.pymongo_kwargs", "name": "pymongo_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_expressions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.update_expressions", "name": "update_expressions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "update_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.queries.update.UpdateQuery.update_query", "name": "update_query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.update.UpdateQuery"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_query of UpdateQuery", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.update_query", "name": "update_query", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.queries.update.UpdateQuery"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_query of UpdateQuery", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "upsert_insert_doc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.update.UpdateQuery.upsert_insert_doc", "name": "upsert_insert_doc", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.update.UpdateQuery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.update.UpdateQuery", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UpdateResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.update.UpdateResponse", "name": "UpdateResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "beanie.odm.queries.update.UpdateResponse", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "beanie.odm.queries.update", "mro": ["beanie.odm.queries.update.UpdateResponse", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NEW_DOCUMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.update.UpdateResponse.NEW_DOCUMENT", "name": "NEW_DOCUMENT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "NEW_DOCUMENT"}, "type_ref": "builtins.str"}}}, "OLD_DOCUMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.update.UpdateResponse.OLD_DOCUMENT", "name": "OLD_DOCUMENT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "OLD_DOCUMENT"}, "type_ref": "builtins.str"}}}, "UPDATE_RESULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.update.UpdateResponse.UPDATE_RESULT", "name": "UPDATE_RESULT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "UPDATE_RESULT"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.update.UpdateResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.queries.update.UpdateResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UpdateResult": {".class": "SymbolTableNode", "cross_ref": "pymongo.results.UpdateResult", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.update.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.update.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.update.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.update.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.update.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.update.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "parse_obj": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.parsing.parse_obj", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/queries/update.py"}