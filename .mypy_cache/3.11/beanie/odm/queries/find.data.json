{".class": "MypyFile", "_fullname": "beanie.odm.queries.find", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregateMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.aggregation_methods.AggregateMethods", "kind": "Gdef"}, "AggregationQuery": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.aggregation.AggregationQuery", "kind": "Gdef"}, "And": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.find.logical.And", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BaseCursorQuery": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.cursor.BaseCursorQuery", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "BulkWriter": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.bulk.BulkWriter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CloneInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.clone.CloneInterface", "kind": "Gdef"}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef"}, "DeleteMany": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.delete.DeleteMany", "kind": "Gdef"}, "DeleteOne": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.delete.DeleteOne", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.DocType", "kind": "Gdef"}, "DocumentNotFound": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.DocumentNotFound", "kind": "Gdef"}, "Encoder": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.encoder.Encoder", "kind": "Gdef"}, "FindMany": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.cursor.BaseCursorQuery"}, "beanie.odm.interfaces.aggregation_methods.AggregateMethods"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.find.FindMany", "name": "FindMany", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.find", "mro": ["beanie.odm.queries.find.FindMany", "beanie.odm.queries.find.FindQuery", "beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "beanie.odm.queries.cursor.BaseCursorQuery", "beanie.odm.interfaces.aggregation_methods.AggregateMethods", "builtins.object"], "names": {".class": "SymbolTable", "DeleteQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindMany.DeleteQueryType", "name": "DeleteQueryType", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["document_model", "find_query", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "beanie.odm.queries.delete.DeleteMany", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.delete.DeleteQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "UpdateQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindMany.UpdateQueryType", "name": "UpdateQueryType", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["document_model", "find_query"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "beanie.odm.queries.update.UpdateMany", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.update.UpdateQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindMany.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FindMany", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindMany.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany._cache_key", "name": "_cache_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_cache_key of FindMany", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany._cache_key", "name": "_cache_key", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_cache_key of FindMany", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany._get_cache", "name": "_get_cache", "type": null}}, "_set_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany._set_cache", "name": "_set_cache", "type": null}}, "aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany.aggregate", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.aggregate", "name": "aggregate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.aggregate", "name": "aggregate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "build_aggregation_pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "extra_stages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany.build_aggregation_pipeline", "name": "build_aggregation_pipeline", "type": null}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.delete_many", "name": "delete_many", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_many of FindMany", "ret_type": "beanie.odm.queries.delete.DeleteMany", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany.find", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "beanie.odm.queries.find.FindMany.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "find_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany.find_many", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "beanie.odm.queries.find.FindMany.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.find_many", "name": "find_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.find_many", "name": "find_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "first_or_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.first_or_none", "name": "first_or_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "first_or_none of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.limit", "name": "limit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "limit of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "limit_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.limit_number", "name": "limit_number", "setter_type": null, "type": "builtins.int"}}, "motor_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.motor_cursor", "name": "motor_cursor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.motor_cursor", "name": "motor_cursor", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "motor_cursor of FindMany", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindMany.project", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "beanie.odm.queries.find.FindMany.project", "name": "project", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.project", "name": "project", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.project", "name": "project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindMany.project", "name": "project", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.project", "name": "project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindMany.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "skip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.skip", "name": "skip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "n"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "skip of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.skip_number", "name": "skip_number", "setter_type": null, "type": "builtins.int"}}, "sort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.sort", "name": "sort", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sort of FindMany", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sort_expressions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindMany.sort_expressions", "name": "sort_expressions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of FindMany", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.update_many", "name": "update_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_many of FindMany", "ret_type": "beanie.odm.queries.update.UpdateMany", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindMany.upsert", "name": "upsert", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindMany.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert of FindMany", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindMany.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindMany.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindMany", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FindQueryResultType"], "typeddict_type": null}}, "FindOne": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.find.FindOne", "name": "FindOne", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindOne", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.find", "mro": ["beanie.odm.queries.find.FindOne", "beanie.odm.queries.find.FindQuery", "beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "DeleteQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindOne.DeleteQueryType", "name": "DeleteQueryType", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["document_model", "find_query", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "beanie.odm.queries.delete.DeleteOne", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.delete.DeleteQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "UpdateQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindOne.UpdateQueryType", "name": "UpdateQueryType", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "beanie.odm.queries.update.UpdateOne", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__await__ of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "beanie.odm.queries.find.FindOne._find_one", "name": "_find_one", "type": null}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.delete_one", "name": "delete_one", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_one of FindOne", "ret_type": "beanie.odm.queries.delete.DeleteOne", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindOne.find_one", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "beanie.odm.queries.find.FindOne.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindOne.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindOne.find_one", "name": "find_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindOne.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindOne.find_one", "name": "find_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "projection_model", "session", "ignore_cache", "fetch_links", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindOne.project", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "beanie.odm.queries.find.FindOne.project", "name": "project", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindOne.project", "name": "project", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindOne.project", "name": "project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "beanie.odm.queries.find.FindOne.project", "name": "project", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindOne.project", "name": "project", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "NoneType"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "project of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "id": -1, "name": "FindQueryProjectionType", "namespace": "beanie.odm.queries.find.FindOne.project#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "replace_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "document", "session", "bulk_writer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.replace_one", "name": "replace_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "document", "session", "bulk_writer"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindOne.replace_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace_one of FindOne", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.UpdateResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindOne.replace_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.queries.update.UpdateResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of FindOne", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.update_one", "name": "update_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "session", "bulk_writer", "response_type", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.queries.update.UpdateResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_one of FindOne", "ret_type": "beanie.odm.queries.update.UpdateOne", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "response_type", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindOne.upsert", "name": "upsert", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 4], "arg_names": ["self", "args", "on_insert", "session", "response_type", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindOne.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.queries.update.UpdateResponse", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert of FindOne", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindOne.upsert", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindOne.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindOne", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FindQueryResultType"], "typeddict_type": null}}, "FindQuery": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["update", 1]], "alt_promote": null, "bases": ["beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.queries.find.FindQuery", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "beanie.odm.queries.find.FindQuery", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.queries.find", "mro": ["beanie.odm.queries.find.FindQuery", "beanie.odm.interfaces.update.UpdateMethods", "beanie.odm.interfaces.session.SessionMethods", "beanie.odm.interfaces.clone.CloneInterface", "builtins.object"], "names": {".class": "SymbolTable", "AggregationQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindQuery.AggregationQueryType", "name": "AggregationQueryType", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["document_model", "aggregation_pipeline", "find_query", "projection_model", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.aggregation.AggregationProjectionType", "id": 1, "name": "AggregationProjectionType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.aggregation.AggregationQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "DeleteQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindQuery.DeleteQueryType", "name": "DeleteQueryType", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "beanie.odm.queries.delete.DeleteOne"}, {".class": "TypeType", "item": "beanie.odm.queries.delete.DeleteMany"}], "uses_pep604_syntax": false}}}, "UpdateQueryType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.queries.find.FindQuery.UpdateQueryType", "name": "UpdateQueryType", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "beanie.odm.queries.update.UpdateQuery"}, {".class": "TypeType", "item": "beanie.odm.queries.update.UpdateMany"}, {".class": "TypeType", "item": "beanie.odm.queries.update.UpdateOne"}], "uses_pep604_syntax": false}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindQuery.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document_model"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FindQuery", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindQuery.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count of FindQuery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindQuery.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of FindQuery", "ret_type": {".class": "UnionType", "items": ["beanie.odm.queries.delete.DeleteOne", "beanie.odm.queries.delete.DeleteMany"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "document_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.document_model", "name": "document_model", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.queries.find.FindQuery.__init__", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}}}, "encoders": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.encoders", "name": "encoders", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.queries.find.FindQuery.exists", "name": "exists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "exists of Find<PERSON>uery", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_links": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.fetch_links", "name": "fetch_links", "setter_type": null, "type": "builtins.bool"}}, "find_expressions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.find_expressions", "name": "find_expressions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_filter_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindQuery.get_filter_query", "name": "get_filter_query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_filter_query of FindQuery", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_projection_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.queries.find.FindQuery.get_projection_model", "name": "get_projection_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_projection_model of FindQuery", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.ignore_cache", "name": "ignore_cache", "setter_type": null, "type": "builtins.bool"}}, "lazy_parse": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.lazy_parse", "name": "lazy_parse", "setter_type": null, "type": "builtins.bool"}}, "nesting_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.nesting_depth", "name": "nesting_depth", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "nesting_depths_per_field": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.nesting_depths_per_field", "name": "nesting_depths_per_field", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "prepare_find_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindQuery.prepare_find_expressions", "name": "prepare_find_expressions", "type": null}}, "project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "projection_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.queries.find.FindQuery.project", "name": "project", "type": null}}, "projection_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.projection_model", "name": "projection_model", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}}}, "pymongo_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "beanie.odm.queries.find.FindQuery.pymongo_kwargs", "name": "pymongo_kwargs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQuery.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "id": 1, "name": "FindQueryResultType", "namespace": "beanie.odm.queries.find.FindQuery", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindQuery"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["FindQueryResultType"], "typeddict_type": null}}, "FindQueryProjectionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryProjectionType", "name": "FindQueryProjectionType", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "FindQueryResultType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.queries.find.FindQueryResultType", "name": "FindQueryResultType", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "LRUCache": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.cache.LRUCache", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ReplaceOne": {".class": "SymbolTableNode", "cross_ref": "pymongo.operations.ReplaceOne", "kind": "Gdef"}, "SessionMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.session.SessionMethods", "kind": "Gdef"}, "SortDirection": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.enums.SortDirection", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateMany": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateMany", "kind": "Gdef"}, "UpdateMethods": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.update.UpdateMethods", "kind": "Gdef"}, "UpdateOne": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateOne", "kind": "Gdef"}, "UpdateQuery": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateQuery", "kind": "Gdef"}, "UpdateResponse": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateResponse", "kind": "Gdef"}, "UpdateResult": {".class": "SymbolTableNode", "cross_ref": "pymongo.results.UpdateResult", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.find.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.find.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.find.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.find.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.find.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.queries.find.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "construct_lookup_queries": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.find.construct_lookup_queries", "kind": "Gdef"}, "convert_ids": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.relations.convert_ids", "kind": "Gdef"}, "get_dict": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.dump.get_dict", "kind": "Gdef"}, "get_projection": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.projection.get_projection", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "parse_obj": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.parsing.parse_obj", "kind": "Gdef"}, "split_text_query": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.find.split_text_query", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/queries/find.py"}