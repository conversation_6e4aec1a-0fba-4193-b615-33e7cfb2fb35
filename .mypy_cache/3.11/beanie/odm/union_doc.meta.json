{"data_mtime": 1752086944, "dep_lines": [7, 8, 9, 10, 11, 6, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["beanie.odm.interfaces.aggregate", "beanie.odm.interfaces.detector", "beanie.odm.interfaces.find", "beanie.odm.interfaces.getters", "beanie.odm.settings.union_doc", "beanie.odm.bulk", "motor.motor_asyncio", "beanie.exceptions", "typing", "builtins", "_frozen_importlib", "abc", "beanie.odm.interfaces", "beanie.odm.settings", "beanie.odm.settings.base", "enum", "motor", "motor.core", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "35a7ed452ef1d55547332132cf643b0429955d75", "id": "beanie.odm.union_doc", "ignore_all": true, "interface_hash": "a3448ba7477e9549dfd8d363cf7a49e923e24596", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/union_doc.py", "plugin_data": null, "size": 2950, "suppressed": [], "version_id": "1.16.1"}