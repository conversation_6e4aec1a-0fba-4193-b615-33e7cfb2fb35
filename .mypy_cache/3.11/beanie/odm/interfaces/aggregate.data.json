{".class": "MypyFile", "_fullname": "beanie.odm.interfaces.aggregate", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregateInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["find_all", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface", "name": "AggregateInterface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.interfaces.aggregate", "mro": ["beanie.odm.interfaces.aggregate.AggregateInterface", "builtins.object"], "names": {".class": "SymbolTable", "aggregate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "name": "aggregate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#0", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#0", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "name": "aggregate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#0", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#0", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "name": "aggregate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate", "name": "aggregate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#0", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#0", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["cls", "aggregation_pipeline", "projection_model", "session", "ignore_cache", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aggregate of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.aggregation.AggregationQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.aggregate.AggregateInterface.aggregate#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "find_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.find_all", "name": "find_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.aggregate.AggregateInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.find_all", "name": "find_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.aggregate.AggregateInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of AggregateInterface", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.AggregateInterface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AggregationQuery": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.aggregation.AggregationQuery", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocType", "name": "DocType", "upper_bound": "beanie.odm.interfaces.aggregate.AggregateInterface", "values": [], "variance": 0}}, "DocumentProjectionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.aggregate.DocumentProjectionType", "name": "DocumentProjectionType", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "FindMany": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.find.FindMany", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.aggregate.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.aggregate.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.aggregate.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.aggregate.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.aggregate.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.aggregate.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/interfaces/aggregate.py"}