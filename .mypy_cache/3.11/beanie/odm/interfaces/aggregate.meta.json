{"data_mtime": 1752086944, "dep_lines": [7, 8, 4, 1, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["beanie.odm.queries.aggregation", "beanie.odm.queries.find", "motor.motor_asyncio", "abc", "typing", "pydantic", "builtins", "_frozen_importlib", "beanie.odm.interfaces.aggregation_methods", "beanie.odm.interfaces.clone", "beanie.odm.interfaces.session", "beanie.odm.interfaces.update", "beanie.odm.queries", "beanie.odm.queries.cursor", "motor", "motor.core", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "d8ac8e1032edd1493636ba96f3b03c4d941a6e30", "id": "beanie.odm.interfaces.aggregate", "ignore_all": true, "interface_hash": "646146b3cf76eb6cd35b940815759140c4f12f52", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/interfaces/aggregate.py", "plugin_data": null, "size": 2384, "suppressed": [], "version_id": "1.16.1"}