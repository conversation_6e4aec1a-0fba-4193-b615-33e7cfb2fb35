{".class": "MypyFile", "_fullname": "beanie.odm.interfaces.find", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.Document", "kind": "Gdef"}, "DocumentProjectionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "name": "DocumentProjectionType", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "FindInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_model_type", 1], ["get_settings", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.interfaces.find.FindInterface", "name": "FindInterface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "beanie.odm.interfaces.find.FindInterface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.interfaces.find", "mro": ["beanie.odm.interfaces.find.FindInterface", "builtins.object"], "names": {".class": "SymbolTable", "_add_class_id_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "args", "with_children"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.interfaces.find.FindInterface._add_class_id_filter", "name": "_add_class_id_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "args", "with_children"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_class_id_filter of FindInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface._add_class_id_filter", "name": "_add_class_id_filter", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "args", "with_children"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_class_id_filter of FindInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "beanie.odm.interfaces.find.FindInterface._children", "name": "_children", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_class_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "beanie.odm.interfaces.find.FindInterface._class_id", "name": "_class_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_find_many_query_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.interfaces.find.FindInterface._find_many_query_class", "name": "_find_many_query_class", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_find_one_query_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.interfaces.find.FindInterface._find_one_query_class", "name": "_find_one_query_class", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_inheritance_inited": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.interfaces.find.FindInterface._inheritance_inited", "name": "_inheritance_inited", "setter_type": null, "type": "builtins.bool"}}, "all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "name": "all", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "name": "all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "name": "all", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "name": "all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "name": "all", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.all", "name": "all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.interfaces.find.FindInterface.count", "name": "count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.count", "name": "count", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.int"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "find_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "name": "find_all", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "name": "find_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "name": "find_all", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "name": "find_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "name": "find_all", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_all", "name": "find_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "skip", "limit", "sort", "projection_model", "session", "ignore_cache", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_all of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_all#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "find_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "name": "find_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "name": "find_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_many", "name": "find_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_many#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "find_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_class"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "name": "find_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "name": "find_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_overload", "is_decorated"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.find_one", "name": "find_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "NoneType"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#0", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of FindInterface", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.interfaces.find.FindInterface.find_one#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "get_model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "beanie.odm.interfaces.find.FindInterface.get_model_type", "name": "get_model_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_type of FindInterface", "ret_type": "beanie.odm.interfaces.detector.ModelType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.get_model_type", "name": "get_model_type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_type of FindInterface", "ret_type": "beanie.odm.interfaces.detector.ModelType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "beanie.odm.interfaces.find.FindInterface.get_settings", "name": "get_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_settings of FindInterface", "ret_type": "beanie.odm.settings.base.ItemSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.interfaces.find.FindInterface.get_settings", "name": "get_settings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.interfaces.find.FindInterface"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_settings of FindInterface", "ret_type": "beanie.odm.settings.base.ItemSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindInterface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.interfaces.find.FindInterface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FindMany": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.find.FindMany", "kind": "Gdef"}, "FindOne": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.find.FindOne", "kind": "Gdef"}, "FindType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.interfaces.find.FindType", "name": "FindType", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.union_doc.UnionDoc", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "ItemSettings": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.settings.base.ItemSettings", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ModelType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.detector.ModelType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SortDirection": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.enums.SortDirection", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnionDoc": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.union_doc.UnionDoc", "kind": "Gdef"}, "View": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.views.View", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.find.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.find.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.find.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.find.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.find.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.interfaces.find.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/interfaces/find.py"}