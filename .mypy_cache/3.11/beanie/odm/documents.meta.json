{"data_mtime": 1752086944, "dep_lines": [79, 80, 68, 69, 70, 71, 72, 73, 89, 90, 91, 92, 93, 94, 103, 104, 109, 50, 55, 56, 57, 58, 74, 115, 25, 32, 33, 35, 36, 42, 1, 2, 3, 4, 5, 21, 23, 26, 34, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["beanie.odm.operators.find.comparison", "beanie.odm.operators.update.general", "beanie.odm.interfaces.aggregate", "beanie.odm.interfaces.detector", "beanie.odm.interfaces.find", "beanie.odm.interfaces.getters", "beanie.odm.interfaces.inheritance", "beanie.odm.interfaces.setters", "beanie.odm.queries.find", "beanie.odm.queries.update", "beanie.odm.settings.document", "beanie.odm.utils.dump", "beanie.odm.utils.parsing", "beanie.odm.utils.pydantic", "beanie.odm.utils.self_validation", "beanie.odm.utils.state", "beanie.odm.utils.typing", "beanie.odm.actions", "beanie.odm.bulk", "beanie.odm.cache", "beanie.odm.enums", "beanie.odm.fields", "beanie.odm.models", "beanie.odm.views", "motor.motor_asyncio", "pydantic.class_validators", "pydantic.main", "pymongo.errors", "pymongo.results", "beanie.exceptions", "asyncio", "warnings", "datetime", "enum", "typing", "uuid", "bson", "pydantic", "pymongo", "typing_extensions", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_warnings", "abc", "annotated_types", "asyncio.tasks", "beanie.odm.interfaces", "beanie.odm.interfaces.aggregation_methods", "beanie.odm.interfaces.clone", "beanie.odm.interfaces.session", "beanie.odm.interfaces.update", "beanie.odm.queries", "beanie.odm.queries.cursor", "beanie.odm.settings", "beanie.odm.settings.base", "beanie.odm.utils", "bson.objectid", "bson.raw_bson", "motor", "motor.core", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.types", "pydantic_core", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re", "types"], "hash": "d5de2adec714b5ffd02c2a84b865b5b10322119e", "id": "beanie.odm.documents", "ignore_all": true, "interface_hash": "d8ad494a3b39502ddc9e0ee1320026cf3be400ba", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/documents.py", "plugin_data": null, "size": 48339, "suppressed": ["lazy_model"], "version_id": "1.16.1"}