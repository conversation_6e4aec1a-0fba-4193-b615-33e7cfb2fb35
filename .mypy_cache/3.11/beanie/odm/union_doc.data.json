{".class": "MypyFile", "_fullname": "beanie.odm.union_doc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregateInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.aggregate.AggregateInterface", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BulkWriter": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.bulk.BulkWriter", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "DetectionInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.detector.DetectionInterface", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FindInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.find.FindInterface", "kind": "Gdef"}, "ModelType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.detector.ModelType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OtherGettersInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.getters.OtherGettersInterface", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UnionDoc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.interfaces.find.FindInterface", "beanie.odm.interfaces.aggregate.AggregateInterface", "beanie.odm.interfaces.getters.OtherGettersInterface", "beanie.odm.interfaces.detector.DetectionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.union_doc.UnionDoc", "name": "UnionDoc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.union_doc.UnionDoc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.union_doc", "mro": ["beanie.odm.union_doc.UnionDoc", "beanie.odm.interfaces.find.FindInterface", "beanie.odm.interfaces.aggregate.AggregateInterface", "beanie.odm.interfaces.getters.OtherGettersInterface", "beanie.odm.interfaces.detector.DetectionInterface", "builtins.object"], "names": {".class": "SymbolTable", "_document_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.union_doc.UnionDoc._document_models", "name": "_document_models", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_inited": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.union_doc.UnionDoc._is_inited", "name": "_is_inited", "setter_type": null, "type": "builtins.bool"}}, "_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "beanie.odm.union_doc.UnionDoc._settings", "name": "_settings", "setter_type": null, "type": "beanie.odm.settings.union_doc.UnionDocSettings"}}, "bulk_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "session", "ordered", "bypass_document_validation", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.union_doc.UnionDoc.bulk_writer", "name": "bulk_writer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "session", "ordered", "bypass_document_validation", "comment"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bulk_writer of UnionDoc", "ret_type": "beanie.odm.bulk.BulkWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.union_doc.UnionDoc.bulk_writer", "name": "bulk_writer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "session", "ordered", "bypass_document_validation", "comment"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bulk_writer of UnionDoc", "ret_type": "beanie.odm.bulk.BulkWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.union_doc.UnionDoc.get_model_type", "name": "get_model_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_type of UnionDoc", "ret_type": "beanie.odm.interfaces.detector.ModelType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.union_doc.UnionDoc.get_model_type", "name": "get_model_type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_type of UnionDoc", "ret_type": "beanie.odm.interfaces.detector.ModelType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.union_doc.UnionDoc.get_settings", "name": "get_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_settings of UnionDoc", "ret_type": "beanie.odm.settings.union_doc.UnionDocSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.union_doc.UnionDoc.get_settings", "name": "get_settings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_settings of UnionDoc", "ret_type": "beanie.odm.settings.union_doc.UnionDocSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "name", "doc_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.union_doc.UnionDoc.register_doc", "name": "register_doc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "name", "doc_model"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_doc of UnionDoc", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.union_doc.UnionDoc.register_doc", "name": "register_doc", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "name", "doc_model"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.union_doc.UnionDoc"}, "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_doc of UnionDoc", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.union_doc.UnionDoc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.union_doc.UnionDoc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnionDocNotInited": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.UnionDocNotInited", "kind": "Gdef"}, "UnionDocSettings": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.settings.union_doc.UnionDocSettings", "kind": "Gdef"}, "UnionDocType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.union_doc.UnionDocType", "name": "UnionDocType", "upper_bound": "beanie.odm.union_doc.UnionDoc", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.union_doc.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.union_doc.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.union_doc.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.union_doc.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.union_doc.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.union_doc.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/union_doc.py"}