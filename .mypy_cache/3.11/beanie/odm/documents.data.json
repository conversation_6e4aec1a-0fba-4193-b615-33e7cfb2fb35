{".class": "MypyFile", "_fullname": "beanie.odm.documents", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActionDirections": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.ActionDirections", "kind": "Gdef"}, "AggregateInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.aggregate.AggregateInterface", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyDocMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 1, "name": "DocType", "namespace": "beanie.odm.documents.AnyDocMethod", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "beanie.odm.documents.P", "id": 2, "name": "P", "namespace": "beanie.odm.documents.AnyDocMethod", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.R", "id": 3, "name": "R", "namespace": "beanie.odm.documents.AnyDocMethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "beanie.odm.documents.AnyDocMethod", "line": 122, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 1, "name": "DocType", "namespace": "beanie.odm.documents.AnyDocMethod", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "beanie.odm.documents.P", "id": 2, "name": "P", "namespace": "beanie.odm.documents.AnyDocMethod", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "beanie.odm.documents.P", "id": 2, "name": "P", "namespace": "beanie.odm.documents.AnyDocMethod", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.R", "id": 3, "name": "R", "namespace": "beanie.odm.documents.AnyDocMethod", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "AsyncDocMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 1, "name": "DocType", "namespace": "beanie.odm.documents.AsyncDocMethod", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "beanie.odm.documents.P", "id": 2, "name": "P", "namespace": "beanie.odm.documents.AsyncDocMethod", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.R", "id": 3, "name": "R", "namespace": "beanie.odm.documents.AsyncDocMethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "beanie.odm.documents.AsyncDocMethod", "line": 124, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 1, "name": "DocType", "namespace": "beanie.odm.documents.AsyncDocMethod", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "beanie.odm.documents.P", "id": 2, "name": "P", "namespace": "beanie.odm.documents.AsyncDocMethod", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "beanie.odm.documents.P", "id": 2, "name": "P", "namespace": "beanie.odm.documents.AsyncDocMethod", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.R", "id": 3, "name": "R", "namespace": "beanie.odm.documents.AsyncDocMethod", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "AsyncIOMotorClientSession": {".class": "SymbolTableNode", "cross_ref": "motor.motor_asyncio.AsyncIOMotorClientSession", "kind": "Gdef"}, "BackLink": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.BackLink", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "BulkWriter": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.bulk.BulkWriter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "CollectionWasNotInitialized": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.CollectionWasNotInitialized", "kind": "Gdef"}, "Concatenate": {".class": "SymbolTableNode", "cross_ref": "typing.Concatenate", "kind": "Gdef"}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef"}, "Coroutine": {".class": "SymbolTableNode", "cross_ref": "typing.Coroutine", "kind": "Gdef"}, "CurrentDate": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.general.CurrentDate", "kind": "Gdef"}, "DBRef": {".class": "SymbolTableNode", "cross_ref": "bson.dbref.DBRef", "kind": "Gdef"}, "DeleteResult": {".class": "SymbolTableNode", "cross_ref": "pymongo.results.DeleteResult", "kind": "Gdef"}, "DeleteRules": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.DeleteRules", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "name": "DocType", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, "Document": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.interfaces.setters.SettersInterface", "beanie.odm.interfaces.inheritance.InheritanceInterface", "beanie.odm.interfaces.find.FindInterface", "beanie.odm.interfaces.aggregate.AggregateInterface", "beanie.odm.interfaces.getters.OtherGettersInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.documents.Document", "name": "Document", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "beanie.odm.documents.Document", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.documents", "mro": ["beanie.odm.documents.Document", "beanie.odm.interfaces.setters.SettersInterface", "beanie.odm.interfaces.inheritance.InheritanceInterface", "beanie.odm.interfaces.find.FindInterface", "beanie.odm.interfaces.aggregate.AggregateInterface", "beanie.odm.interfaces.getters.OtherGettersInterface", "builtins.object"], "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.documents.Document.Config", "name": "Config", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.Document.Config", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.documents", "mro": ["beanie.odm.documents.Document.Config", "builtins.object"], "names": {".class": "SymbolTable", "allow_population_by_field_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.Config.allow_population_by_field_name", "name": "allow_population_by_field_name", "setter_type": null, "type": "builtins.bool"}}, "fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.Config.fields", "name": "fields", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "json_encoders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.Config.json_encoders", "name": "json_encoders", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["oid"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "bson.objectid.ObjectId", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "ObjectId", "ret_type": "bson.objectid.ObjectId", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["object"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["object", "encoding", "errors"], "arg_types": ["typing_extensions.Buffer", "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": "str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "schema_extra": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.Config.schema_extra", "name": "schema_extra", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0], "arg_names": ["schema", "model"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeType", "item": "beanie.odm.documents.Document"}], "imprecise_arg_kinds": false, "variables": []}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Config.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document.Config", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.documents.Document.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["beanie.odm.documents.Document", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Document", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document._cache", "name": "_cache", "setter_type": null, "type": {".class": "UnionType", "items": ["beanie.odm.cache.LRUCache", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_check_hidden_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.documents.Document._check_hidden_fields", "name": "_check_hidden_fields", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document._check_hidden_fields", "name": "_check_hidden_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_hidden_fields of Document", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_collect_updates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "old_dict", "new_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.documents.Document._collect_updates", "name": "_collect_updates", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "old_dict", "new_dict"], "arg_types": ["beanie.odm.documents.Document", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_collect_updates of Document", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_database_major_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document._database_major_version", "name": "_database_major_version", "setter_type": null, "type": "builtins.int"}}, "_document_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document._document_settings", "name": "_document_settings", "setter_type": null, "type": {".class": "UnionType", "items": ["beanie.odm.settings.document.DocumentSettings", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_fill_back_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.documents.Document._fill_back_refs", "name": "_fill_back_refs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document._fill_back_refs", "name": "_fill_back_refs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "values"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fill_back_refs of Document", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_link_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document._link_fields", "name": "_link_fields", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "beanie.odm.fields.LinkInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_previous_saved_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document._previous_saved_state", "name": "_previous_saved_state", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_save_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.documents.Document._save_state", "name": "_save_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_save_state of Document", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_saved_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document._saved_state", "name": "_saved_state", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "bulk_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "session", "ordered", "bypass_document_validation", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.bulk_writer", "name": "bulk_writer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "session", "ordered", "bypass_document_validation", "comment"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bulk_writer of Document", "ret_type": "beanie.odm.bulk.BulkWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.bulk_writer", "name": "bulk_writer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "session", "ordered", "bypass_document_validation", "comment"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bulk_writer of Document", "ret_type": "beanie.odm.bulk.BulkWriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "beanie.odm.documents.Document.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "current_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "expression", "session", "bulk_writer", "skip_sync", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.Document.current_date", "name": "current_date", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "expression", "session", "bulk_writer", "skip_sync", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["datetime.datetime", "beanie.odm.fields.ExpressionField", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_date of Document", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "arg_types": ["beanie.odm.documents.Document", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.DeleteRules", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": [null, "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "arg_types": ["beanie.odm.documents.Document", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.DeleteRules", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["cls", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.delete_all", "name": "delete_all", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["cls", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_all of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.delete_all", "name": "delete_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["cls", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_all of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "distinct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "key", "filter", "session", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.distinct", "name": "distinct", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "key", "filter", "session", "kwargs"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "distinct of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.distinct", "name": "distinct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "key", "filter", "session", "kwargs"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "distinct of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fetch_all_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "beanie.odm.documents.Document.fetch_all_links", "name": "fetch_all_links", "type": null}}, "fetch_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.fetch_link", "name": "fetch_link", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field"], "arg_types": ["beanie.odm.documents.Document", {".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fetch_link of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fill_back_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "beanie.odm.documents.Document.fill_back_refs", "name": "fill_back_refs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.fill_back_refs", "name": "fill_back_refs", "setter_type": null, "type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "document_id", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "document_id", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.get", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.get", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.get", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.get", "name": "get", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "document_id", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.get", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.get", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.get", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "get_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_changes", "name": "get_changes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_changes of Document", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.get_changes", "name": "get_changes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_changes of Document", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_link_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_link_fields", "name": "get_link_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_link_fields of Document", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "beanie.odm.fields.LinkInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.get_link_fields", "name": "get_link_fields", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_link_fields of Document", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "beanie.odm.fields.LinkInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_model_type", "name": "get_model_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_type of Document", "ret_type": "beanie.odm.interfaces.detector.ModelType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.get_model_type", "name": "get_model_type", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_type of Document", "ret_type": "beanie.odm.interfaces.detector.ModelType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_previous_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_previous_changes", "name": "get_previous_changes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_previous_changes of Document", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.get_previous_changes", "name": "get_previous_changes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_previous_changes of Document", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_previous_saved_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_previous_saved_state", "name": "get_previous_saved_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_previous_saved_state of Document", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_saved_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_saved_state", "name": "get_saved_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_saved_state of Document", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.get_settings", "name": "get_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_settings of Document", "ret_type": "beanie.odm.settings.document.DocumentSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.get_settings", "name": "get_settings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_settings of Document", "ret_type": "beanie.odm.settings.document.DocumentSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.has_changed", "name": "has_changed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_changed of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.has_changed", "name": "has_changed", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_changed of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.id", "name": "id", "setter_type": null, "type": {".class": "UnionType", "items": ["beanie.odm.fields.PydanticObjectId", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "inc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "expression", "session", "bulk_writer", "skip_sync", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.Document.inc", "name": "inc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "expression", "session", "bulk_writer", "skip_sync", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.fields.ExpressionField", "builtins.float", "builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inc of Document", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "link_rule", "session", "skip_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.insert", "name": "insert", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "link_rule", "session", "skip_actions"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "beanie.odm.fields.WriteRules", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "insert of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.insert", "name": "insert", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": [null, "link_rule", "session", "skip_actions"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98392, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "beanie.odm.fields.WriteRules", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "insert of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98392, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98392, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "insert_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "documents", "session", "link_rule", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.insert_many", "name": "insert_many", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "documents", "session", "link_rule", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "insert_many of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pymongo.results.InsertManyResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.insert_many", "name": "insert_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["cls", "documents", "session", "link_rule", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "insert_many of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pymongo.results.InsertManyResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "insert_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["cls", "document", "session", "bulk_writer", "link_rule"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.insert_one", "name": "insert_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["cls", "document", "session", "bulk_writer", "link_rule"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "insert_one of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.insert_one", "name": "insert_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["cls", "document", "session", "bulk_writer", "link_rule"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "insert_one of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.insert_one", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "inspect_collection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.inspect_collection", "name": "inspect_collection", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "session"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inspect_collection of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "beanie.odm.models.InspectionResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.inspect_collection", "name": "inspect_collection", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "session"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "inspect_collection of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "beanie.odm.models.InspectionResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.is_changed", "name": "is_changed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_changed of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.is_changed", "name": "is_changed", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_changed of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "link_from_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.link_from_id", "name": "link_from_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "id"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "link_from_id of Document", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.link_from_id", "name": "link_from_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "id"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "link_from_id of Document", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.model_config", "name": "model_config", "setter_type": null, "type": {".class": "TypedDictType", "fallback": "pydantic.config.ConfigDict", "items": [["title", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["model_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["field_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["str_strip_whitespace", "builtins.bool"], ["str_min_length", "builtins.int"], ["str_max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ExtraValues"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["use_enum_values", "builtins.bool"], ["validate_assignment", "builtins.bool"], ["arbitrary_types_allowed", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["alias_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["ignored_types", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["allow_inf_nan", "builtins.bool"], ["json_schema_extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_encoders", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strict", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], "uses_pep604_syntax": false}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["val_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strings"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["validate_return", "builtins.bool"], ["protected_namespaces", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["hide_input_in_errors", "builtins.bool"], ["defer_build", "builtins.bool"], ["experimental_defer_build_mode", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type_adapter"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["plugin_settings", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["schema_generator", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_schema_serialization_defaults_required", "builtins.bool"], ["json_schema_mode_override", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}], ["validation_error_cause", "builtins.bool"], ["use_attribute_docstrings", "builtins.bool"], ["cache_strings", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": []}}}, "replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "ignore_revision", "session", "bulk_writer", "link_rule", "skip_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.replace", "name": "replace", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "ignore_revision", "session", "bulk_writer", "link_rule", "skip_actions"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.replace", "name": "replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": [null, "ignore_revision", "session", "bulk_writer", "link_rule", "skip_actions"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98408, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98408, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98408, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "replace_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "documents", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.replace_many", "name": "replace_many", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "documents", "session"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.replace_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.replace_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace_many of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.replace_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.replace_many", "name": "replace_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "documents", "session"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.replace_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.replace_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace_many of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": -1, "name": "DocType", "namespace": "beanie.odm.documents.Document.replace_many", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "revision_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.Document.revision_id", "name": "revision_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rollback of Document", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.rollback", "name": "rollback", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["beanie.odm.documents.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rollback of Document", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "session", "link_rule", "ignore_revision", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "session", "link_rule", "ignore_revision", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.save", "name": "save", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": [null, "session", "link_rule", "ignore_revision", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98420, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.WriteRules", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98420, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98420, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "save_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "ignore_revision", "session", "bulk_writer", "skip_actions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.save_changes", "name": "save_changes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "ignore_revision", "session", "bulk_writer", "skip_actions"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_changes of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.save_changes", "name": "save_changes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": [null, "ignore_revision", "session", "bulk_writer", "skip_actions"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98432, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_changes of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98432, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98432, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "expression", "session", "bulk_writer", "skip_sync", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.Document.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "expression", "session", "bulk_writer", "skip_sync", "kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.fields.ExpressionField", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set of Document", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}, "state_management_replace_objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.state_management_replace_objects", "name": "state_management_replace_objects", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state_management_replace_objects of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.state_management_replace_objects", "name": "state_management_replace_objects", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state_management_replace_objects of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state_management_save_previous": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.state_management_save_previous", "name": "state_management_save_previous", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state_management_save_previous of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.state_management_save_previous", "name": "state_management_save_previous", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state_management_save_previous of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "merge_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.sync", "name": "sync", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "merge_strategy"], "arg_types": ["beanie.odm.documents.Document", "beanie.odm.documents.MergeStrategy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sync of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.Document.to_ref", "name": "to_ref", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "ignore_revision", "session", "bulk_writer", "skip_actions", "skip_sync", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "beanie.odm.documents.Document.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "args", "ignore_revision", "session", "bulk_writer", "skip_actions", "skip_sync", "pymongo_kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 4], "arg_names": [null, "args", "ignore_revision", "session", "bulk_writer", "skip_actions", "skip_sync", "pymongo_kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98441, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98441, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocType", "id": 98441, "name": "DocType", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}]}}}}, "update_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["cls", "args", "session", "bulk_writer", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.update_all", "name": "update_all", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["cls", "args", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_all of Document", "ret_type": "beanie.odm.queries.update.UpdateMany", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.update_all", "name": "update_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 4], "arg_names": ["cls", "args", "session", "bulk_writer", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_all of Document", "ret_type": "beanie.odm.queries.update.UpdateMany", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_state_management": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.use_state_management", "name": "use_state_management", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "use_state_management of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.use_state_management", "name": "use_state_management", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "use_state_management of Document", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_self": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "beanie.odm.documents.Document.validate_self", "name": "validate_self", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["beanie.odm.documents.Document", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_self of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.Document.validate_self", "name": "validate_self", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": [null, "args", "kwargs"], "arg_types": ["beanie.odm.documents.Document", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": true, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_self of Document", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.Document.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.Document", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DocumentNotFound": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.DocumentNotFound", "kind": "Gdef"}, "DocumentProjectionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "name": "DocumentProjectionType", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "DocumentSettings": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.settings.document.DocumentSettings", "kind": "Gdef"}, "DocumentWasNotSaved": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.DocumentWasNotSaved", "kind": "Gdef"}, "DocumentWithSoftDelete": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.documents.Document"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.documents.DocumentWithSoftDelete", "name": "DocumentWithSoftDelete", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "beanie.odm.documents", "mro": ["beanie.odm.documents.DocumentWithSoftDelete", "beanie.odm.documents.Document", "beanie.odm.interfaces.setters.SettersInterface", "beanie.odm.interfaces.inheritance.InheritanceInterface", "beanie.odm.interfaces.find.FindInterface", "beanie.odm.interfaces.aggregate.AggregateInterface", "beanie.odm.interfaces.getters.OtherGettersInterface", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "arg_types": ["beanie.odm.documents.DocumentWithSoftDelete", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.DeleteRules", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of DocumentWithSoftDelete", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deleted_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.deleted_at", "name": "deleted_at", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "find_many": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "name": "find_many", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of DocumentWithSoftDelete", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "name": "find_many", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many of DocumentWithSoftDelete", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "find_many_in_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "name": "find_many_in_all", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many_in_all of DocumentWithSoftDelete", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "name": "find_many_in_all", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "skip", "limit", "sort", "session", "ignore_cache", "fetch_links", "with_children", "lazy_parse", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "beanie.odm.enums.SortDirection"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_many_in_all of DocumentWithSoftDelete", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindMany"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_many_in_all", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "find_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "name": "find_one", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of DocumentWithSoftDelete", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "name": "find_one", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["cls", "args", "projection_model", "session", "ignore_cache", "fetch_links", "with_children", "nesting_depth", "nesting_depths_per_field", "pymongo_kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_one of DocumentWithSoftDelete", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "beanie.odm.queries.find.FindOne"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "id": -1, "name": "FindType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentProjectionType", "id": -2, "name": "DocumentProjectionType", "namespace": "beanie.odm.documents.DocumentWithSoftDelete.find_one", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "hard_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.hard_delete", "name": "hard_delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "session", "bulk_writer", "link_rule", "skip_actions", "pymongo_kwargs"], "arg_types": ["beanie.odm.documents.DocumentWithSoftDelete", {".class": "UnionType", "items": ["motor.motor_asyncio.AsyncIOMotorClientSession", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["beanie.odm.bulk.BulkWriter", {".class": "NoneType"}], "uses_pep604_syntax": false}, "beanie.odm.fields.DeleteRules", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["beanie.odm.actions.ActionDirections", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "hard_delete of DocumentWithSoftDelete", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["pymongo.results.DeleteResult", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.documents.DocumentWithSoftDelete.is_deleted", "name": "is_deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.documents.DocumentWithSoftDelete"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_deleted of DocumentWithSoftDelete", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.DocumentWithSoftDelete.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.DocumentWithSoftDelete", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateKeyError": {".class": "SymbolTableNode", "cross_ref": "pymongo.errors.<PERSON><PERSON><PERSON>ey<PERSON>rror", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EventTypes": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.EventTypes", "kind": "Gdef"}, "ExpressionField": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.ExpressionField", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef"}, "FindInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.find.FindInterface", "kind": "Gdef"}, "FindMany": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.find.FindMany", "kind": "Gdef"}, "FindOne": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.find.FindOne", "kind": "Gdef"}, "FindType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.FindType", "name": "FindType", "upper_bound": {".class": "UnionType", "items": ["beanie.odm.documents.Document", "beanie.odm.views.View"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "IS_PYDANTIC_V2": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.IS_PYDANTIC_V2", "kind": "Gdef"}, "In": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.find.comparison.In", "kind": "Gdef"}, "Inc": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.general.Inc", "kind": "Gdef"}, "InheritanceInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.inheritance.InheritanceInterface", "kind": "Gdef"}, "InsertManyResult": {".class": "SymbolTableNode", "cross_ref": "pymongo.results.InsertManyResult", "kind": "Gdef"}, "InsertOne": {".class": "SymbolTableNode", "cross_ref": "pymongo.operations.InsertOne", "kind": "Gdef"}, "InspectionError": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.models.InspectionError", "kind": "Gdef"}, "InspectionResult": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.models.InspectionResult", "kind": "Gdef"}, "InspectionStatuses": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.enums.InspectionStatuses", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LRUCache": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.cache.LRUCache", "kind": "Gdef"}, "LazyModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "beanie.odm.documents.LazyModel", "name": "LazyModel", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "beanie.odm.documents.LazyModel", "source_any": null, "type_of_any": 3}}}, "Link": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.Link", "kind": "Gdef"}, "LinkInfo": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.LinkInfo", "kind": "Gdef"}, "LinkTypes": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.LinkTypes", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MergeStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.documents.MergeStrategy", "name": "MergeStrategy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "beanie.odm.documents.MergeStrategy", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "beanie.odm.documents", "mro": ["beanie.odm.documents.MergeStrategy", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.MergeStrategy.local", "name": "local", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "local"}, "type_ref": "builtins.str"}}}, "remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.documents.MergeStrategy.remote", "name": "remote", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "remote"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.MergeStrategy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.documents.MergeStrategy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.detector.ModelType", "kind": "Gdef"}, "NotSupported": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.NotSupported", "kind": "Gdef"}, "ObjectId": {".class": "SymbolTableNode", "cross_ref": "bson.objectid.ObjectId", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OtherGettersInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.getters.OtherGettersInterface", "kind": "Gdef"}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.P", "name": "P", "upper_bound": "builtins.object", "variance": 0}}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef"}, "PrivateAttr": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.PrivateAttr", "kind": "Gdef"}, "PydanticObjectId": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.PydanticObjectId", "kind": "Gdef"}, "R": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.documents.R", "name": "R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "ReplaceError": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.ReplaceError", "kind": "Gdef"}, "RevisionIdWasChanged": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.RevisionIdWasChanged", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "SetOperator": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.general.Set", "kind": "Gdef"}, "SetRevisionId": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.general.SetRevisionId", "kind": "Gdef"}, "SettersInterface": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.setters.SettersInterface", "kind": "Gdef"}, "SortDirection": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.enums.SortDirection", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Unset": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.update.general.Unset", "kind": "Gdef"}, "UpdateMany": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateMany", "kind": "Gdef"}, "UpdateResponse": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.queries.update.UpdateResponse", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.ValidationError", "kind": "Gdef"}, "View": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.views.View", "kind": "Gdef"}, "WriteRules": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.WriteRules", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.documents.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.documents.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.documents.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.documents.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.documents.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.documents.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "apply_changes": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.parsing.apply_changes", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "document_alias_generator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.document_alias_generator", "name": "document_alias_generator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "document_alias_generator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_id_class": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.typing.extract_id_class", "kind": "Gdef"}, "get_dict": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.dump.get_dict", "kind": "Gdef"}, "get_extra_field_info": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.get_extra_field_info", "kind": "Gdef"}, "get_field_type": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.get_field_type", "kind": "Gdef"}, "get_model_dump": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.get_model_dump", "kind": "Gdef"}, "get_model_fields": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.get_model_fields", "kind": "Gdef"}, "get_top_level_nones": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.dump.get_top_level_nones", "kind": "Gdef"}, "json_schema_extra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["schema", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.documents.json_schema_extra", "name": "json_schema_extra", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["schema", "model"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeType", "item": "beanie.odm.documents.Document"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "json_schema_extra", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_models": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.parsing.merge_models", "kind": "Gdef"}, "model_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic.functional_validators.model_validator", "kind": "Gdef"}, "parse_model": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.parse_model", "kind": "Gdef"}, "parse_object_as": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.parse_object_as", "kind": "Gdef"}, "previous_saved_state_needed": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.state.previous_saved_state_needed", "kind": "Gdef"}, "root_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.class_validators.root_validator", "name": "root_validator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "save_state_after": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.state.save_state_after", "kind": "Gdef"}, "saved_state_needed": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.state.saved_state_needed", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "uuid4": {".class": "SymbolTableNode", "cross_ref": "uuid.uuid4", "kind": "Gdef"}, "validate_self_before": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.self_validation.validate_self_before", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wrap_with_actions": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.actions.wrap_with_actions", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/documents.py"}