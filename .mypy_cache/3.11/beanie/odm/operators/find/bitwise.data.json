{".class": "MypyFile", "_fullname": "beanie.odm.operators.find.bitwise", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseFindBitwiseOperator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.operators.find.BaseFindOperator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "name": "BaseFindBitwiseOperator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "beanie.odm.operators.find.bitwise", "mro": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "beanie.odm.operators.find.BaseFindOperator", "beanie.odm.operators.BaseOperator", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "bitmask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "field", "bitmask"], "arg_types": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", {".class": "UnionType", "items": ["builtins.str", "beanie.odm.fields.ExpressionField"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseFindBitwiseOperator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bitmask": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.bitmask", "name": "bitmask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "field": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.field", "name": "field", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "beanie.odm.fields.ExpressionField"], "uses_pep604_syntax": false}}}, "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.operator", "name": "operator", "setter_type": null, "type": "builtins.str"}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.query", "name": "query", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.query", "name": "query", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "query of BaseFindBitwiseOperator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseFindOperator": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.operators.find.BaseFindOperator", "kind": "Gdef"}, "BitsAllClear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.operators.find.bitwise.BitsAllClear", "name": "BitsAllClear", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.operators.find.bitwise.BitsAllClear", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "beanie.odm.operators.find.bitwise", "mro": ["beanie.odm.operators.find.bitwise.BitsAllClear", "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "beanie.odm.operators.find.BaseFindOperator", "beanie.odm.operators.BaseOperator", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.operators.find.bitwise.BitsAllClear.operator", "name": "operator", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.operators.find.bitwise.BitsAllClear.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.operators.find.bitwise.BitsAllClear", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitsAllSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.operators.find.bitwise.BitsAllSet", "name": "BitsAllSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.operators.find.bitwise.BitsAllSet", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "beanie.odm.operators.find.bitwise", "mro": ["beanie.odm.operators.find.bitwise.BitsAllSet", "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "beanie.odm.operators.find.BaseFindOperator", "beanie.odm.operators.BaseOperator", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.operators.find.bitwise.BitsAllSet.operator", "name": "operator", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.operators.find.bitwise.BitsAllSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.operators.find.bitwise.BitsAllSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitsAnyClear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.operators.find.bitwise.BitsAnyClear", "name": "BitsAnyClear", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.operators.find.bitwise.BitsAnyClear", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "beanie.odm.operators.find.bitwise", "mro": ["beanie.odm.operators.find.bitwise.BitsAnyClear", "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "beanie.odm.operators.find.BaseFindOperator", "beanie.odm.operators.BaseOperator", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.operators.find.bitwise.BitsAnyClear.operator", "name": "operator", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.operators.find.bitwise.BitsAnyClear.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.operators.find.bitwise.BitsAnyClear", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BitsAnySet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "beanie.odm.operators.find.bitwise.BitsAnySet", "name": "BitsAnySet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "beanie.odm.operators.find.bitwise.BitsAnySet", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "beanie.odm.operators.find.bitwise", "mro": ["beanie.odm.operators.find.bitwise.BitsAnySet", "beanie.odm.operators.find.bitwise.BaseFindBitwiseOperator", "beanie.odm.operators.find.BaseFindOperator", "beanie.odm.operators.BaseOperator", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "beanie.odm.operators.find.bitwise.BitsAnySet.operator", "name": "operator", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "beanie.odm.operators.find.bitwise.BitsAnySet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "beanie.odm.operators.find.bitwise.BitsAnySet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExpressionField": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.fields.ExpressionField", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.operators.find.bitwise.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.operators.find.bitwise.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.operators.find.bitwise.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.operators.find.bitwise.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.operators.find.bitwise.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.operators.find.bitwise.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/operators/find/bitwise.py"}