{".class": "MypyFile", "_fullname": "beanie.odm.utils.parsing", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApplyChangesException": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.ApplyChangesException", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocWasNotRegisteredInUnionClass": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.DocWasNotRegisteredInUnionClass", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.documents.Document", "kind": "Gdef"}, "ModelType": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.interfaces.detector.ModelType", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnionHasNoRegisteredDocs": {".class": "SymbolTableNode", "cross_ref": "beanie.exceptions.UnionHasNoRegisteredDocs", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.utils.parsing.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.utils.parsing.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.utils.parsing.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.utils.parsing.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.utils.parsing.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "beanie.odm.utils.parsing.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "apply_changes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["changes", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.utils.parsing.apply_changes", "name": "apply_changes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["changes", "target"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["pydantic.main.BaseModel", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "apply_changes", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_config_value": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.get_config_value", "kind": "Gdef"}, "merge_models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["left", "right"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.utils.parsing.merge_models", "name": "merge_models", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["left", "right"], "arg_types": ["pydantic.main.BaseModel", "pydantic.main.BaseModel"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge_models", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_model": {".class": "SymbolTableNode", "cross_ref": "beanie.odm.utils.pydantic.parse_model", "kind": "Gdef"}, "parse_obj": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["model", "data", "lazy_parse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.utils.parsing.parse_obj", "name": "parse_obj", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["model", "data", "lazy_parse"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "beanie.odm.documents.Document"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_obj", "ret_type": "pydantic.main.BaseModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "beanie.odm.utils.parsing.save_state", "name": "save_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["item"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_state", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/utils/parsing.py"}