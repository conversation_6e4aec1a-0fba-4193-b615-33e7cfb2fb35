{"data_mtime": 1752086944, "dep_lines": [27, 26, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 22, 23, 25, 54, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 10, 10, 10, 10, 10, 5, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["beanie.odm.utils.pydantic", "beanie.odm.fields", "dataclasses", "datetime", "decimal", "enum", "ipaddress", "operator", "pathlib", "re", "uuid", "typing", "bson", "pydantic", "beanie", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc", "array", "beanie.odm.documents", "beanie.odm.interfaces", "beanie.odm.interfaces.aggregate", "beanie.odm.interfaces.find", "beanie.odm.interfaces.getters", "beanie.odm.interfaces.inheritance", "beanie.odm.interfaces.setters", "bson.binary", "bson.dbref", "bson.decimal128", "bson.max_key", "bson.min_key", "bson.objectid", "bson.regex", "mmap", "os", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.networks", "pydantic.types", "pydantic_core._pydantic_core", "typing_extensions"], "hash": "4a85ce96147de3125225020fc53620b993a9844a", "id": "beanie.odm.utils.encoder", "ignore_all": true, "interface_hash": "9b06c2c8d7101c0ca99c2d9b4bd110fc7c0d5b34", "mtime": 1751599594, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/beanie/odm/utils/encoder.py", "plugin_data": null, "size": 5261, "suppressed": [], "version_id": "1.16.1"}