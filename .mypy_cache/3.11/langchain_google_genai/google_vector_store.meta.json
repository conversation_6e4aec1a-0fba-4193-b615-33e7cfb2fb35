{"data_mtime": 1752086944, "dep_lines": [21, 21, 22, 23, 24, 25, 28, 29, 9, 10, 11, 21, 26, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 5, 5, 10, 5, 10, 5, 5, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.ai.generativelanguage", "google.ai", "langchain_core.documents", "langchain_core.embeddings", "langchain_core.runnables", "langchain_core.vectorstores", "langchain_google_genai._genai_extension", "langchain_google_genai.genai_aqa", "asyncio", "functools", "typing", "google", "pydantic", "langchain_google_genai", "builtins", "_frozen_importlib", "abc", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.retriever_service", "google.ai.generativelanguage_v1beta.services.retriever_service.client", "langchain_core", "langchain_core.documents.base", "langchain_core.embeddings.embeddings", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables.base", "langchain_core.vectorstores.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.fields", "pydantic.main"], "hash": "8f4297d3614d0970912e9bd4d7b5093fadcde4b0", "id": "langchain_google_genai.google_vector_store", "ignore_all": true, "interface_hash": "03659b637dbd137942fd3afae5516f6aefdb153f", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/google_vector_store.py", "plugin_data": null, "size": 16148, "suppressed": [], "version_id": "1.16.1"}