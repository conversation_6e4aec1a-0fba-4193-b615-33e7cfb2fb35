{"data_mtime": 1752086944, "dep_lines": [22, 21, 22, 26, 30, 21, 24, 32, 1, 3, 4, 5, 6, 7, 21, 31, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23], "dep_prios": [10, 10, 20, 5, 5, 20, 5, 5, 5, 10, 10, 10, 10, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage", "google.ai.generativelanguage_v1beta", "langchain_core.utils.function_calling", "langchain_core.utils.json_schema", "google.ai", "langchain_core.tools", "pydantic.v1", "__future__", "collections", "importlib", "json", "logging", "typing", "google", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "google.ai.generativelanguage_v1beta.types.content", "langchain_core", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables", "langchain_core.runnables.base", "langchain_core.tools.base", "langchain_core.utils", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "377136575e3309071aba470f2946fbcbebfc1b13", "id": "langchain_google_genai._function_utils", "ignore_all": true, "interface_hash": "d2295b6b1bad532f315d4d1f4683177339edbe2c", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/_function_utils.py", "plugin_data": null, "size": 21648, "suppressed": ["proto"], "version_id": "1.16.1"}