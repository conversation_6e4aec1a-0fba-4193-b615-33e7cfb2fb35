{"data_mtime": 1752086944, "dep_lines": [39, 36, 59, 64, 75, 76, 78, 79, 88, 92, 93, 250, 32, 63, 65, 77, 84, 85, 86, 87, 101, 102, 111, 117, 128, 133, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 32, 94, 102, 109, 133, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 31, 35], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta", "langchain_core.callbacks.manager", "langchain_core.language_models.chat_models", "langchain_core.messages.ai", "langchain_core.messages.tool", "langchain_core.output_parsers.base", "langchain_core.output_parsers.openai_tools", "langchain_core.utils.function_calling", "langchain_core.utils.pydantic", "langchain_core.utils.utils", "google.api_core.exceptions", "google.api_core", "langchain_core.language_models", "langchain_core.messages", "langchain_core.output_parsers", "langchain_core.outputs", "langchain_core.runnables", "langchain_core.tools", "langchain_core.utils", "pydantic.v1", "tenacity.retry", "langchain_google_genai._common", "langchain_google_genai._function_utils", "langchain_google_genai._image_utils", "langchain_google_genai._genai_extension", "__future__", "asyncio", "base64", "io", "json", "logging", "mimetypes", "uuid", "warnings", "wave", "difflib", "operator", "typing", "google", "pydantic", "tenacity", "typing_extensions", "langchain_google_genai", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "annotated_types", "enum", "google.ai", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.generative_service.async_client", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.safety", "google.api_core.client_info", "google.api_core.gapic_v1", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth", "google.auth._credentials_base", "google.auth.credentials", "langchain_core", "langchain_core.caches", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.language_models.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.messages.function", "langchain_core.messages.human", "langchain_core.outputs.chat_generation", "langchain_core.outputs.chat_result", "langchain_core.outputs.generation", "langchain_core.prompt_values", "langchain_core.rate_limiters", "langchain_core.runnables.base", "langchain_core.runnables.config", "langchain_core.tools.base", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "re", "types"], "hash": "562ff017f435b3c2b1033f5b67034d00725da3f0", "id": "langchain_google_genai.chat_models", "ignore_all": true, "interface_hash": "6108f92c0f36e29427e65cc110364035f2697bea", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/chat_models.py", "plugin_data": null, "size": 73558, "suppressed": ["filetype", "proto"], "version_id": "1.16.1"}