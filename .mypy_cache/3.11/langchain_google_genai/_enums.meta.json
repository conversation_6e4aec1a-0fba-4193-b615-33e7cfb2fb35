{"data_mtime": 1752086944, "dep_lines": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["google.ai.generativelanguage_v1beta", "google.ai", "google", "builtins", "_frozen_importlib", "abc", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.safety", "typing"], "hash": "dfc09a01f8d4f7775e1044681a1db8cbb8bf6d3f", "id": "langchain_google_genai._enums", "ignore_all": true, "interface_hash": "4a0666a35ed8a34dda369526641e28b5589d03bc", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/_enums.py", "plugin_data": null, "size": 252, "suppressed": [], "version_id": "1.16.1"}