{".class": "MypyFile", "_fullname": "langchain_google_genai._function_utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "BaseModelV1": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.BaseModel", "kind": "Gdef"}, "BaseTool": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tools.base.BaseTool", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FunctionDescription": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.function_calling.FunctionDescription", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_ENUM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_google_genai._function_utils.TYPE_ENUM", "name": "TYPE_ENUM", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ALLOWED_SCHEMA_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_google_genai._function_utils._ALLOWED_SCHEMA_FIELDS", "name": "_ALLOWED_SCHEMA_FIELDS", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_ALLOWED_SCHEMA_FIELDS_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_google_genai._function_utils._ALLOWED_SCHEMA_FIELDS_SET", "name": "_ALLOWED_SCHEMA_FIELDS_SET", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_CodeExecutionLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._CodeExecutionLike", "line": 70, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.CodeExecution", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "_FunctionCallingConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai._function_utils._FunctionCallingConfigDict", "name": "_FunctionCallingConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._FunctionCallingConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_google_genai._function_utils", "mro": ["langchain_google_genai._function_utils._FunctionCallingConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["mode", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig.Mode", "builtins.str"], "uses_pep604_syntax": false}], ["allowed_function_names", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["allowed_function_names", "mode"]}}}, "_FunctionDeclarationLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._FunctionDeclarationLike", "line": 62, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["langchain_core.tools.base.BaseTool", {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "_GoogleSearchLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._GoogleSearchLike", "line": 69, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Tool.GoogleSearch", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "_GoogleSearchRetrievalLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._GoogleSearchRetrievalLike", "line": 65, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.GoogleSearchRetrieval", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "_ToolChoiceType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._ToolChoiceType", "line": 461, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "any"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "uses_pep604_syntax": false}}}, "_ToolConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai._function_utils._ToolConfigDict", "name": "_ToolConfigDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._ToolConfigDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_google_genai._function_utils", "mro": ["langchain_google_genai._function_utils._ToolConfigDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["function_calling_config", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._FunctionCallingConfigDict"}]], "readonly_keys": [], "required_keys": ["function_calling_config"]}}}, "_ToolDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai._function_utils._ToolDict", "name": "_ToolDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._ToolDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_google_genai._function_utils", "mro": ["langchain_google_genai._function_utils._ToolDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["function_declarations", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._FunctionDeclarationLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], ["google_search_retrieval", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._GoogleSearchRetrievalLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["google_search", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._GoogleSearchLike"}], ["code_execution", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._CodeExecutionLike"}]], "readonly_keys": [], "required_keys": ["function_declarations", "google_search_retrieval"]}}}, "_ToolType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._ToolType", "line": 83, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Tool", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._ToolDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._FunctionDeclarationLike"}], "uses_pep604_syntax": false}}}, "_ToolsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_google_genai._function_utils._ToolsType", "line": 84, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._ToolType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai._function_utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai._function_utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai._function_utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai._function_utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai._function_utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai._function_utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_convert_pydantic_to_genai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["pydantic_model", "tool_name", "tool_description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._convert_pydantic_to_genai_function", "name": "_convert_pydantic_to_genai_function", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["pydantic_model", "tool_name", "tool_description"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_pydantic_to_genai_function", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dict_to_gapic_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._dict_to_gapic_schema", "name": "_dict_to_gapic_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_dict_to_gapic_schema", "ret_type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.content.Schema", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_base_tool_to_function_declaration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._format_base_tool_to_function_declaration", "name": "_format_base_tool_to_function_declaration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": ["langchain_core.tools.base.BaseTool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_base_tool_to_function_declaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_dict_to_function_declaration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._format_dict_to_function_declaration", "name": "_format_dict_to_function_declaration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_dict_to_function_declaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_json_schema_to_gapic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._format_json_schema_to_gapic", "name": "_format_json_schema_to_gapic", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_json_schema_to_gapic", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_to_gapic_function_declaration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._format_to_gapic_function_declaration", "name": "_format_to_gapic_function_declaration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._FunctionDeclarationLike"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_to_gapic_function_declaration", "ret_type": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_def_key_from_schema_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_def_key_from_schema_path", "name": "_get_def_key_from_schema_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema_path"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_def_key_from_schema_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_items_from_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_items_from_schema", "name": "_get_items_from_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_items_from_schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_items_from_schema_any": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_items_from_schema_any", "name": "_get_items_from_schema_any", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_items_from_schema_any", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_nullable_type_from_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_nullable_type_from_schema", "name": "_get_nullable_type_from_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_nullable_type_from_schema", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_properties_from_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_properties_from_schema", "name": "_get_properties_from_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_properties_from_schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_properties_from_schema_any": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_properties_from_schema_any", "name": "_get_properties_from_schema_any", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_properties_from_schema_any", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_type_from_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._get_type_from_schema", "name": "_get_type_from_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_type_from_schema", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_nullable_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._is_nullable_schema", "name": "_is_nullable_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_nullable_schema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tool_choice_to_tool_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tool_choice", "all_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils._tool_choice_to_tool_config", "name": "_tool_choice_to_tool_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tool_choice", "all_names"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._ToolChoiceType"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_tool_choice_to_tool_config", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._ToolConfigDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "callable_as_lc_tool": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tools.convert.tool", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "convert_to_genai_function_declarations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils.convert_to_genai_function_declarations", "name": "convert_to_genai_function_declarations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tools"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._ToolsType"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_genai_function_declarations", "ret_type": "google.ai.generativelanguage_v1beta.types.content.Tool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_openai_tool": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.function_calling.convert_to_openai_tool", "kind": "Gdef"}, "dereference_refs": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.json_schema.dereference_refs", "kind": "Gdef"}, "gapic": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types", "kind": "Gdef"}, "glm": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "is_basemodel_subclass_safe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils.is_basemodel_subclass_safe", "name": "is_basemodel_subclass_safe", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_basemodel_subclass_safe", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_google_genai._function_utils.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "proto": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langchain_google_genai._function_utils.proto", "name": "proto", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langchain_google_genai._function_utils.proto", "source_any": null, "type_of_any": 3}}}, "replace_defs_in_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["original_schema", "defs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils.replace_defs_in_schema", "name": "replace_defs_in_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["original_schema", "defs"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "replace_defs_in_schema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_import": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["module_name", "attribute_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils.safe_import", "name": "safe_import", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["module_name", "attribute_name"], "arg_types": ["builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "safe_import", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tool_to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai._function_utils.tool_to_dict", "name": "tool_to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": ["google.ai.generativelanguage_v1beta.types.content.Tool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tool_to_dict", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_google_genai._function_utils._ToolDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/_function_utils.py"}