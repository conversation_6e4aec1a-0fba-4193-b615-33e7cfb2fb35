{"data_mtime": 1752086944, "dep_lines": [13, 15, 21, 22, 23, 24, 24, 13, 21, 24, 7, 8, 9, 10, 11, 13, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [10, 5, 10, 10, 10, 10, 10, 20, 20, 20, 10, 10, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["google.ai.generativelanguage", "google.ai.generativelanguage_v1beta", "google.api_core.client_options", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.auth.credentials", "google.auth.exceptions", "google.ai", "google.api_core", "google.auth", "datetime", "logging", "re", "dataclasses", "typing", "google", "langchain_core", "builtins", "_frozen_importlib", "abc", "enum", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.generative_service.async_client", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.services.retriever_service", "google.ai.generativelanguage_v1beta.services.retriever_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.retriever", "google.ai.generativelanguage_v1beta.types.retriever_service", "google.ai.generativelanguage_v1beta.types.safety", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.auth._credentials_base"], "hash": "4cca3c6bd0ab2c793ef788082766807714f26032", "id": "langchain_google_genai._genai_extension", "ignore_all": true, "interface_hash": "85743d028a548af2862f78fc233a9b8d7e0fc24e", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/_genai_extension.py", "plugin_data": null, "size": 20775, "suppressed": ["google.protobuf"], "version_id": "1.16.1"}