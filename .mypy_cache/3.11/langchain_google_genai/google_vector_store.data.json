{".class": "MypyFile", "_fullname": "langchain_google_genai.google_vector_store", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AqaInput": {".class": "SymbolTableNode", "cross_ref": "langchain_google_genai.genai_aqa.AqaInput", "kind": "Gdef"}, "AqaOutput": {".class": "SymbolTableNode", "cross_ref": "langchain_google_genai.genai_aqa.AqaOutput", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "langchain_core.documents.base.Document", "kind": "Gdef"}, "DoesNotExistsException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai.google_vector_store.DoesNotExistsException", "name": "DoesNotExistsException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai.google_vector_store.DoesNotExistsException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_google_genai.google_vector_store", "mro": ["langchain_google_genai.google_vector_store.DoesNotExistsException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "corpus_id", "document_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.DoesNotExistsException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "corpus_id", "document_id"], "arg_types": ["langchain_google_genai.google_vector_store.DoesNotExistsException", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DoesNotExistsException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_google_genai.google_vector_store.DoesNotExistsException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_google_genai.google_vector_store.DoesNotExistsException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Embeddings": {".class": "SymbolTableNode", "cross_ref": "langchain_core.embeddings.embeddings.Embeddings", "kind": "Gdef"}, "GenAIAqa": {".class": "SymbolTableNode", "cross_ref": "langchain_google_genai.genai_aqa.GenAIAqa", "kind": "Gdef"}, "GoogleVectorStore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.vectorstores.base.VectorStore"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore", "name": "GoogleVectorStore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_google_genai.google_vector_store", "mro": ["langchain_google_genai.google_vector_store.GoogleVectorStore", "langchain_core.vectorstores.base.VectorStore", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 4], "arg_names": ["self", "corpus_id", "document_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 4], "arg_names": ["self", "corpus_id", "document_id", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of GoogleVectorStore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_retriever": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore._retriever", "name": "_retriever", "setter_type": null, "type": "langchain_google_genai.google_vector_store._SemanticRetriever"}}, "_select_relevance_score_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore._select_relevance_score_fn", "name": "_select_relevance_score_fn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_select_relevance_score_fn of GoogleVectorStore", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_texts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 4], "arg_names": ["self", "texts", "metadatas", "document_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.add_texts", "name": "add_texts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 4], "arg_names": ["self", "texts", "metadatas", "document_id", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_texts of GoogleVectorStore", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.adelete", "name": "adelete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adelete of GoogleVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_aqa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.as_aqa", "name": "as_aqa", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "as_aqa of GoogleVectorStore", "ret_type": {".class": "Instance", "args": ["builtins.str", "langchain_google_genai.genai_aqa.AqaOutput"], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "corpus_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.corpus_id", "name": "corpus_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "corpus_id of GoogleVectorStore", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.corpus_id", "name": "corpus_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "corpus_id of GoogleVectorStore", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_corpus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "corpus_id", "display_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.create_corpus", "name": "create_corpus", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "corpus_id", "display_name"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store.GoogleVectorStore"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_corpus of GoogleVectorStore", "ret_type": "langchain_google_genai.google_vector_store.GoogleVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.create_corpus", "name": "create_corpus", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "corpus_id", "display_name"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store.GoogleVectorStore"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_corpus of GoogleVectorStore", "ret_type": "langchain_google_genai.google_vector_store.GoogleVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["cls", "corpus_id", "document_id", "display_name", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.create_document", "name": "create_document", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["cls", "corpus_id", "document_id", "display_name", "metadata"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store.GoogleVectorStore"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_document of GoogleVectorStore", "ret_type": "langchain_google_genai.google_vector_store.GoogleVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.create_document", "name": "create_document", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["cls", "corpus_id", "document_id", "display_name", "metadata"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store.GoogleVectorStore"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_document of GoogleVectorStore", "ret_type": "langchain_google_genai.google_vector_store.GoogleVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of GoogleVectorStore", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "document_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.document_id", "name": "document_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "document_id of GoogleVectorStore", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.document_id", "name": "document_id", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "document_id of GoogleVectorStore", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_texts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 5, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "corpus_id", "document_id", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.from_texts", "name": "from_texts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "corpus_id", "document_id", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store.GoogleVectorStore"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["langchain_core.embeddings.embeddings.Embeddings", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_texts of GoogleVectorStore", "ret_type": "langchain_google_genai.google_vector_store.GoogleVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.from_texts", "name": "from_texts", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 5, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "corpus_id", "document_id", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store.GoogleVectorStore"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["langchain_core.embeddings.embeddings.Embeddings", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_texts of GoogleVectorStore", "ret_type": "langchain_google_genai.google_vector_store.GoogleVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of GoogleVectorStore", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of GoogleVectorStore", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "similarity_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "query", "k", "filter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.similarity_search", "name": "similarity_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "query", "k", "filter", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search of GoogleVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "similarity_search_with_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "query", "k", "filter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.similarity_search_with_score", "name": "similarity_search_with_score", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "query", "k", "filter", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store.GoogleVectorStore", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search_with_score of GoogleVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_google_genai.google_vector_store.GoogleVectorStore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_google_genai.google_vector_store.GoogleVectorStore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PrivateAttr": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.PrivateAttr", "kind": "Gdef"}, "Runnable": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.Runnable", "kind": "Gdef"}, "RunnableLambda": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.RunnableLambda", "kind": "Gdef"}, "RunnablePassthrough": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.passthrough.RunnablePassthrough", "kind": "Gdef"}, "ServerSideEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.embeddings.embeddings.Embeddings"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai.google_vector_store.ServerSideEmbedding", "name": "ServerSideEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai.google_vector_store.ServerSideEmbedding", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_google_genai.google_vector_store", "mro": ["langchain_google_genai.google_vector_store.ServerSideEmbedding", "langchain_core.embeddings.embeddings.Embeddings", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "embed_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.ServerSideEmbedding.embed_documents", "name": "embed_documents", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "arg_types": ["langchain_google_genai.google_vector_store.ServerSideEmbedding", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_documents of ServerSideEmbedding", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store.ServerSideEmbedding.embed_query", "name": "embed_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["langchain_google_genai.google_vector_store.ServerSideEmbedding", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_query of ServerSideEmbedding", "ret_type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_google_genai.google_vector_store.ServerSideEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_google_genai.google_vector_store.ServerSideEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "VectorStore": {".class": "SymbolTableNode", "cross_ref": "langchain_core.vectorstores.base.VectorStore", "kind": "Gdef"}, "_SemanticRetriever": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever", "name": "_SemanticRetriever", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 173, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 176, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 179, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 58, "name": "name", "type": "langchain_google_genai._genai_extension.EntityName"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 59, "name": "_client", "type": "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "langchain_google_genai.google_vector_store", "mro": ["langchain_google_genai.google_vector_store._SemanticRetriever", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 4], "arg_names": ["self", "client", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 4], "arg_names": ["self", "client", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store._SemanticRetriever", "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _SemanticRetriever", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "name", "_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "name", "_client"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_google_genai._genai_extension.EntityName", "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of _<PERSON><PERSON><PERSON><PERSON>etriever", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "name", "_client"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_google_genai._genai_extension.EntityName", "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of _<PERSON><PERSON><PERSON><PERSON>etriever", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever._client", "name": "_client", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient"}}, "add_texts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "texts", "metadatas", "document_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.add_texts", "name": "add_texts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "texts", "metadatas", "document_id"], "arg_types": ["langchain_google_genai.google_vector_store._SemanticRetriever", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_texts of _SemanticRetriever", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_google_genai.google_vector_store._SemanticRetriever", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of _SemanticRetriever", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "corpus_id", "document_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.from_ids", "name": "from_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "corpus_id", "document_id"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store._SemanticRetriever"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_ids of _SemanticRetriever", "ret_type": "langchain_google_genai.google_vector_store._SemanticRetriever", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.from_ids", "name": "from_ids", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "corpus_id", "document_id"], "arg_types": [{".class": "TypeType", "item": "langchain_google_genai.google_vector_store._SemanticRetriever"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_ids of _SemanticRetriever", "ret_type": "langchain_google_genai.google_vector_store._SemanticRetriever", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.name", "name": "name", "setter_type": null, "type": "langchain_google_genai._genai_extension.EntityName"}}, "similarity_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "k", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.similarity_search", "name": "similarity_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "k", "filter"], "arg_types": ["langchain_google_genai.google_vector_store._SemanticRetriever", "builtins.str", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search of _SemanticRetriever", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_google_genai.google_vector_store._SemanticRetriever.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_google_genai.google_vector_store._SemanticRetriever", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai.google_vector_store.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai.google_vector_store.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai.google_vector_store.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai.google_vector_store.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai.google_vector_store.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_google_genai.google_vector_store.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_delete_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3], "arg_names": ["corpus_id", "document_id", "chunk_id", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai.google_vector_store._delete_chunk", "name": "_delete_chunk", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3], "arg_names": ["corpus_id", "document_id", "chunk_id", "client"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_delete_chunk", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_toAqaInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_google_genai.google_vector_store._toAqaInput", "name": "_toAqaInput", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["input"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_toAqaInput", "ret_type": "langchain_google_genai.genai_aqa.AqaInput", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "genai": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage", "kind": "Gdef"}, "genaix": {".class": "SymbolTableNode", "cross_ref": "langchain_google_genai._genai_extension", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/google_vector_store.py"}