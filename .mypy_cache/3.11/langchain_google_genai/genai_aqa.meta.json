{"data_mtime": 1752086944, "dep_lines": [11, 13, 11, 12, 16, 9, 11, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 10, 5, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.ai.generativelanguage", "langchain_core.runnables.config", "google.ai", "langchain_core.runnables", "langchain_google_genai._genai_extension", "typing", "google", "pydantic", "langchain_google_genai", "builtins", "_frozen_importlib", "abc", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.safety", "google.api_core", "google.api_core.client_info", "google.api_core.gapic_v1", "google.api_core.gapic_v1.client_info", "google.auth", "google.auth._credentials_base", "google.auth.credentials", "langchain_core", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.fields", "pydantic.main", "uuid"], "hash": "22b1a4091eae43eb770fa13e9a4d39392a1afcbe", "id": "langchain_google_genai.genai_aqa", "ignore_all": true, "interface_hash": "ab672ba37e38688563e74ce6187d033667654f08", "mtime": 1751599598, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_google_genai/genai_aqa.py", "plugin_data": null, "size": 4300, "suppressed": [], "version_id": "1.16.1"}