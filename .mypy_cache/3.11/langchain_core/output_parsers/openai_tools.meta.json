{"data_mtime": 1752086943, "dep_lines": [13, 15, 17, 18, 11, 12, 16, 3, 4, 5, 7, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.messages.tool", "langchain_core.output_parsers.transform", "langchain_core.utils.json", "langchain_core.utils.pydantic", "langchain_core.exceptions", "langchain_core.messages", "langchain_core.outputs", "copy", "json", "logging", "typing", "pydantic", "builtins", "_frozen_importlib", "abc", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.output_parsers.base", "langchain_core.outputs.generation", "langchain_core.runnables", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "ed314ad374478e3c8ad1ae082ccc0a96974ed4db", "id": "langchain_core.output_parsers.openai_tools", "ignore_all": true, "interface_hash": "177220860974ca8aea55cf66711efb20e5eaacb3", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/output_parsers/openai_tools.py", "plugin_data": null, "size": 11059, "suppressed": [], "version_id": "1.16.1"}