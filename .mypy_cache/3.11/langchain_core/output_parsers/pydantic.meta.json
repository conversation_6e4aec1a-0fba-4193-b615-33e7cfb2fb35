{"data_mtime": 1752086943, "dep_lines": [13, 10, 11, 12, 3, 4, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.utils.pydantic", "langchain_core.exceptions", "langchain_core.output_parsers", "langchain_core.outputs", "json", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.output_parsers.base", "langchain_core.output_parsers.json", "langchain_core.output_parsers.transform", "langchain_core.outputs.generation", "langchain_core.runnables", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "90655054ae6346f459eee4bf85c17bf81b58ae15", "id": "langchain_core.output_parsers.pydantic", "ignore_all": true, "interface_hash": "af59ad1e8b9845bdda617e3c650bb501928ac4d2", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/output_parsers/pydantic.py", "plugin_data": null, "size": 4347, "suppressed": [], "version_id": "1.16.1"}