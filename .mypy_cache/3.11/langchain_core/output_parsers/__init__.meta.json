{"data_mtime": 1752086943, "dep_lines": [21, 26, 30, 36, 41, 42, 43, 47, 18, 16, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 30, 30], "dependencies": ["langchain_core.output_parsers.base", "langchain_core.output_parsers.json", "langchain_core.output_parsers.list", "langchain_core.output_parsers.openai_tools", "langchain_core.output_parsers.pydantic", "langchain_core.output_parsers.string", "langchain_core.output_parsers.transform", "langchain_core.output_parsers.xml", "langchain_core._import_utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "cf7f2a5942a2a5d3101e6420901ac9934a29f2d0", "id": "langchain_core.output_parsers", "ignore_all": true, "interface_hash": "44bdb0b9b69712cd82a996a2f743b07bcb32871c", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/output_parsers/__init__.py", "plugin_data": null, "size": 2873, "suppressed": [], "version_id": "1.16.1"}