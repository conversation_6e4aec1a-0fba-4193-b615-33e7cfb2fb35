{"data_mtime": 1752086943, "dep_lines": [6, 15, 16, 6, 7, 13, 14, 3, 4, 5, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 54, 212], "dep_prios": [5, 5, 5, 20, 5, 5, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["xml.etree.ElementTree", "langchain_core.output_parsers.transform", "langchain_core.runnables.utils", "xml.etree", "collections.abc", "langchain_core.exceptions", "langchain_core.messages", "contextlib", "re", "xml", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "enum", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.output_parsers.base", "langchain_core.runnables", "langchain_core.runnables.base", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "c983e8da5ec8a4df598571e8829367d32ea86d34", "id": "langchain_core.output_parsers.xml", "ignore_all": true, "interface_hash": "9b08f3b32385ca923ce488f19e6385bf5546f0da", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/output_parsers/xml.py", "plugin_data": null, "size": 11051, "suppressed": ["defusedxml.ElementTree", "defusedxml"], "version_id": "1.16.1"}