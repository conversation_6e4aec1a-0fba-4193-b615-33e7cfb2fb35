{".class": "MypyFile", "_fullname": "langchain_core.utils.function_calling", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AIMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.ai.AIMessage", "kind": "Gdef"}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.base.BaseMessage", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "BaseModelV1": {".class": "SymbolTableNode", "cross_ref": "pydantic.v1.main.BaseModel", "kind": "Gdef"}, "BaseTool": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tools.base.BaseTool", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FunctionDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.utils.function_calling.FunctionDescription", "name": "FunctionDescription", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling.FunctionDescription", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_core.utils.function_calling", "mro": ["langchain_core.utils.function_calling.FunctionDescription", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["description", "builtins.str"], ["parameters", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["description", "name", "parameters"]}}}, "HumanMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.human.HumanMessage", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PYTHON_TO_JSON_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling.PYTHON_TO_JSON_TYPES", "name": "PYTHON_TO_JSON_TYPES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ToolDescription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.utils.function_calling.ToolDescription", "name": "ToolDescription", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling.ToolDescription", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_core.utils.function_calling", "mro": ["langchain_core.utils.function_calling.ToolDescription", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "ToolMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.tool.ToolMessage", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_MAX_TYPED_DICT_RECURSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling._MAX_TYPED_DICT_RECURSION", "name": "_MAX_TYPED_DICT_RECURSION", "setter_type": null, "type": "builtins.int"}}, "_WellKnownOpenAITools": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling._WellKnownOpenAITools", "name": "_WellKnownOpenAITools", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.utils.function_calling.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.utils.function_calling.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.utils.function_calling.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.utils.function_calling.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.utils.function_calling.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.utils.function_calling.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_convert_any_typed_dicts_to_pydantic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["type_", "visited", "depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._convert_any_typed_dicts_to_pydantic", "name": "_convert_any_typed_dicts_to_pydantic", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["type_", "visited", "depth"], "arg_types": ["builtins.type", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_any_typed_dicts_to_pydantic", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_json_schema_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["schema", "name", "description", "rm_titles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._convert_json_schema_to_openai_function", "name": "_convert_json_schema_to_openai_function", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["schema", "name", "description", "rm_titles"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_json_schema_to_openai_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_pydantic_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["model", "name", "description", "rm_titles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._convert_pydantic_to_openai_function", "name": "_convert_pydantic_to_openai_function", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["model", "name", "description", "rm_titles"], "arg_types": ["builtins.type", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_pydantic_to_openai_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_python_function_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["function"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._convert_python_function_to_openai_function", "name": "_convert_python_function_to_openai_function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["function"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_python_function_to_openai_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_typed_dict_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["typed_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._convert_typed_dict_to_openai_function", "name": "_convert_typed_dict_to_openai_function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["typed_dict"], "arg_types": ["builtins.type"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_typed_dict_to_openai_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_tool_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._format_tool_to_openai_function", "name": "_format_tool_to_openai_function", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": ["langchain_core.tools.base.BaseTool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_format_tool_to_openai_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_python_function_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["function"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._get_python_function_name", "name": "_get_python_function_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["function"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_python_function_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_google_docstring": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["docstring", "args", "error_on_invalid_docstring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._parse_google_docstring", "name": "_parse_google_docstring", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["docstring", "args", "error_on_invalid_docstring"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_parse_google_docstring", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_py_38_safe_origin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._py_38_safe_origin", "name": "_py_38_safe_origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["origin"], "arg_types": ["builtins.type"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_py_38_safe_origin", "ret_type": "builtins.type", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_recursive_set_additional_properties_false": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._recursive_set_additional_properties_false", "name": "_recursive_set_additional_properties_false", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_recursive_set_additional_properties_false", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_rm_titles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["kv", "prev_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling._rm_titles", "name": "_rm_titles", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["kv", "prev_key"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_rm_titles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "beta": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.beta_decorator.beta", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "convert_pydantic_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling.convert_pydantic_to_openai_function", "name": "convert_pydantic_to_openai_function", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["model", "name", "description", "rm_titles"], "arg_types": ["builtins.type", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_pydantic_to_openai_tool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["model", "name", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.utils.function_calling.convert_pydantic_to_openai_tool", "name": "convert_pydantic_to_openai_tool", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["model", "name", "description"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_pydantic_to_openai_tool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.ToolDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.utils.function_calling.convert_pydantic_to_openai_tool", "name": "convert_pydantic_to_openai_tool", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["model", "name", "description"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_pydantic_to_openai_tool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.ToolDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_python_function_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling.convert_python_function_to_openai_function", "name": "convert_python_function_to_openai_function", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["function"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["schema", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling.convert_to_json_schema", "name": "convert_to_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["schema", "strict"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "langchain_core.tools.base.BaseTool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_json_schema", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["function", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling.convert_to_openai_function", "name": "convert_to_openai_function", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["function", "strict"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.type", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "langchain_core.tools.base.BaseTool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_openai_function", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_openai_tool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["tool", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.utils.function_calling.convert_to_openai_tool", "name": "convert_to_openai_tool", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["tool", "strict"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "langchain_core.tools.base.BaseTool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_openai_tool", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.deprecated", "kind": "Gdef"}, "dereference_refs": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.json_schema.dereference_refs", "kind": "Gdef"}, "format_tool_to_openai_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling.format_tool_to_openai_function", "name": "format_tool_to_openai_function", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": ["langchain_core.tools.base.BaseTool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.FunctionDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_tool_to_openai_tool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.utils.function_calling.format_tool_to_openai_tool", "name": "format_tool_to_openai_tool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": ["langchain_core.tools.base.BaseTool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format_tool_to_openai_tool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.ToolDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.utils.function_calling.format_tool_to_openai_tool", "name": "format_tool_to_openai_tool", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool"], "arg_types": ["langchain_core.tools.base.BaseTool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format_tool_to_openai_tool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.utils.function_calling.ToolDescription"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_basemodel_subclass": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.pydantic.is_basemodel_subclass", "kind": "Gdef"}, "is_typeddict": {".class": "SymbolTableNode", "cross_ref": "typing.is_typeddict", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.utils.function_calling.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "tool_example_to_messages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["input", "tool_calls", "tool_outputs", "ai_response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.utils.function_calling.tool_example_to_messages", "name": "tool_example_to_messages", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["input", "tool_calls", "tool_outputs", "ai_response"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["pydantic.main.BaseModel"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tool_example_to_messages", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.utils.function_calling.tool_example_to_messages", "name": "tool_example_to_messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["input", "tool_calls", "tool_outputs", "ai_response"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["pydantic.main.BaseModel"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tool_example_to_messages", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/utils/function_calling.py"}