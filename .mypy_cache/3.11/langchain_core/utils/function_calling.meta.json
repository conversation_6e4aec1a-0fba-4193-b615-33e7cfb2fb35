{"data_mtime": 1752086943, "dep_lines": [28, 29, 23, 26, 27, 32, 3, 5, 6, 7, 8, 9, 10, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.utils.json_schema", "langchain_core.utils.pydantic", "pydantic.v1", "langchain_core._api", "langchain_core.messages", "langchain_core.tools", "__future__", "collections", "inspect", "logging", "types", "typing", "uuid", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "langchain_core._api.beta_decorator", "langchain_core._api.deprecation", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.runnables", "langchain_core.runnables.base", "langchain_core.tools.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "6e1aea4235e30383129fa9a6181f5b79b30a933b", "id": "langchain_core.utils.function_calling", "ignore_all": true, "interface_hash": "eb99953817fd61e93d78b65681d95ebf2f0159b5", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/utils/function_calling.py", "plugin_data": null, "size": 28479, "suppressed": [], "version_id": "1.16.1"}