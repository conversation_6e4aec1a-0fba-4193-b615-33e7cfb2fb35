{"data_mtime": 1752086943, "dep_lines": [41, 44, 32, 33, 34, 35, 22, 24, 25, 26, 27, 29, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.runnables.config", "langchain_core.callbacks.manager", "langchain_core._api", "langchain_core.callbacks", "langchain_core.documents", "langchain_core.runnables", "__future__", "warnings", "abc", "inspect", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_warnings", "langchain_core._api.deprecation", "langchain_core.callbacks.base", "langchain_core.documents.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.main", "types", "uuid"], "hash": "2c33180dfce0bb93a6102bfab21490fb432dfd97", "id": "langchain_core.retrievers", "ignore_all": true, "interface_hash": "e20a9b5b6ce29637863d308ee860417dccf5c8bf", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/retrievers.py", "plugin_data": null, "size": 16735, "suppressed": [], "version_id": "1.16.1"}