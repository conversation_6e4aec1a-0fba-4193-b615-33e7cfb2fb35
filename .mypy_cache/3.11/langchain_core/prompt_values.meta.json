{"data_mtime": 1752086943, "dep_lines": [15, 10, 16, 7, 9, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.load.serializable", "collections.abc", "langchain_core.messages", "__future__", "abc", "typing", "typing_extensions", "builtins", "_frozen_importlib", "langchain_core.load", "langchain_core.messages.ai", "langchain_core.messages.base", "langchain_core.messages.chat", "langchain_core.messages.function", "langchain_core.messages.human", "langchain_core.messages.system", "langchain_core.messages.tool", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "715c4205781be8d49d2dc1071c38175e1c1db9f8", "id": "langchain_core.prompt_values", "ignore_all": true, "interface_hash": "81600f894017e08113428a9133d833544f4ca62b", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/prompt_values.py", "plugin_data": null, "size": 4002, "suppressed": [], "version_id": "1.16.1"}