{"data_mtime": 1752086943, "dep_lines": [2877, 43, 44, 49, 63, 64, 84, 85, 86, 89, 93, 94, 97, 98, 100, 104, 105, 675, 1366, 2173, 2527, 12, 21, 43, 99, 3, 5, 6, 7, 8, 9, 10, 11, 23, 24, 25, 26, 40, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 25, 20, 20, 25, 20, 20, 20, 20, 5, 5, 20, 20, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.beta.runnables.context", "langchain_core._api.beta_decorator", "langchain_core.load.serializable", "langchain_core.runnables.config", "langchain_core.runnables.graph", "langchain_core.runnables.utils", "langchain_core.utils.aiter", "langchain_core.utils.iter", "langchain_core.utils.pydantic", "langchain_core.callbacks.manager", "langchain_core.prompts.base", "langchain_core.runnables.fallbacks", "langchain_core.runnables.retry", "langchain_core.runnables.schema", "langchain_core.tracers.log_stream", "langchain_core.tracers.root_listeners", "langchain_core.tracers.schemas", "langchain_core.runnables.passthrough", "langchain_core.tracers.event_stream", "langchain_core.tracers._streaming", "langchain_core.runnables.configurable", "collections.abc", "concurrent.futures", "langchain_core._api", "langchain_core.tools", "__future__", "asyncio", "collections", "contextlib", "functools", "inspect", "threading", "abc", "itertools", "operator", "types", "typing", "pydantic", "typing_extensions", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "_typeshed", "annotated_types", "asyncio.locks", "asyncio.mixins", "asyncio.tasks", "langchain_core.beta", "langchain_core.beta.runnables", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.load", "langchain_core.prompts", "langchain_core.tools.base", "langchain_core.tracers", "langchain_core.tracers.base", "langchain_core.tracers.core", "langchain_core.utils", "langsmith", "langsmith.run_trees", "langsmith.schemas", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.main", "pydantic.types", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "re", "uuid"], "hash": "e7c6ec70c548def9a575247c471c07089aed3f4b", "id": "langchain_core.runnables.base", "ignore_all": true, "interface_hash": "b93f52946dd3e93da34ef250189a76600f5255ca", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/base.py", "plugin_data": null, "size": 221479, "suppressed": [], "version_id": "1.16.1"}