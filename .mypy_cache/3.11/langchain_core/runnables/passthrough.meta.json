{"data_mtime": 1752086943, "dep_lines": [21, 27, 35, 39, 40, 41, 46, 50, 8, 3, 5, 6, 7, 9, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 25, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.runnables.base", "langchain_core.runnables.config", "langchain_core.runnables.utils", "langchain_core.utils.aiter", "langchain_core.utils.iter", "langchain_core.utils.pydantic", "langchain_core.callbacks.manager", "langchain_core.runnables.graph", "collections.abc", "__future__", "asyncio", "inspect", "threading", "typing", "pydantic", "typing_extensions", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.locks", "asyncio.mixins", "asyncio.tasks", "contextlib", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.utils", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types", "uuid"], "hash": "b94b138323994267d3006e84c54bc6f649a885af", "id": "langchain_core.runnables.passthrough", "ignore_all": true, "interface_hash": "36a04cedb266443ca917b5bec6d5e47a49f69ddb", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/passthrough.py", "plugin_data": null, "size": 25974, "suppressed": [], "version_id": "1.16.1"}