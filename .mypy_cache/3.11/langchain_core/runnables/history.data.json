{".class": "MypyFile", "_fullname": "langchain_core.runnables.history", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseChatMessageHistory": {".class": "SymbolTableNode", "cross_ref": "langchain_core.chat_history.BaseChatMessageHistory", "kind": "Gdef"}, "BaseMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.base.BaseMessage", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConfigurableFieldSpec": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec", "kind": "Gdef"}, "GenericAlias": {".class": "SymbolTableNode", "cross_ref": "types.GenericAlias", "kind": "Gdef"}, "GetSessionHistoryCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_core.runnables.history.GetSessionHistoryCallable", "line": 38, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "langchain_core.chat_history.BaseChatMessageHistory", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "LanguageModelLike": {".class": "SymbolTableNode", "cross_ref": "langchain_core.language_models.base.LanguageModelLike", "kind": "Gdef"}, "MessagesOrDictWithMessages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_core.runnables.history.MessagesOrDictWithMessages", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Output": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.utils.Output", "kind": "Gdef"}, "Run": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.schemas.Run", "kind": "Gdef"}, "Runnable": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.Runnable", "kind": "Gdef"}, "RunnableBindingBase": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.RunnableBindingBase", "kind": "Gdef"}, "RunnableConfig": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.config.RunnableConfig", "kind": "Gdef"}, "RunnableLambda": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.RunnableLambda", "kind": "Gdef"}, "RunnablePassthrough": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.passthrough.RunnablePassthrough", "kind": "Gdef"}, "RunnableWithMessageHistory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.RunnableBindingBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory", "name": "RunnableWithMessageHistory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 173, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 176, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 179, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 2471, "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 5275, "name": "bound", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 5278, "name": "kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 5286, "name": "config", "type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 5289, "name": "config_factories", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 5295, "name": "custom_input_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 5301, "name": "custom_output_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 234, "name": "get_session_history", "type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.GetSessionHistoryCallable"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 235, "name": "input_messages_key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 236, "name": "output_messages_key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 237, "name": "history_messages_key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 238, "name": "history_factory_config", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "langchain_core.runnables.history", "mro": ["langchain_core.runnables.history.RunnableWithMessageHistory", "langchain_core.runnables.base.RunnableBindingBase", "langchain_core.runnables.base.RunnableSerializable", "langchain_core.load.serializable.Serializable", "pydantic.main.BaseModel", "langchain_core.runnables.base.Runnable", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "OutputType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.OutputType", "name": "OutputType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "OutputType of RunnableWithMessageHistory", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -1, "name": "Output", "namespace": "langchain_core.runnables.history.RunnableWithMessageHistory.OutputType", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -1, "name": "Output", "namespace": "langchain_core.runnables.history.RunnableWithMessageHistory.OutputType", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.OutputType", "name": "OutputType", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "OutputType of RunnableWithMessageHistory", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -1, "name": "Output", "namespace": "langchain_core.runnables.history.RunnableWithMessageHistory.OutputType", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -1, "name": "Output", "namespace": "langchain_core.runnables.history.RunnableWithMessageHistory.OutputType", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 4], "arg_names": ["self", "runnable", "get_session_history", "input_messages_key", "output_messages_key", "history_messages_key", "history_factory_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 4], "arg_names": ["self", "runnable", "get_session_history", "input_messages_key", "output_messages_key", "history_messages_key", "history_factory_config", "kwargs"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", "langchain_core.messages.base.BaseMessage", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.MessagesOrDictWithMessages"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", "langchain_core.messages.base.BaseMessage", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.MessagesOrDictWithMessages"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.language_models.base.LanguageModelLike"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.GetSessionHistoryCallable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RunnableWithMessageHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__match_args__-redefinition": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.history.RunnableWithMessageHistory.__match_args__", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__match_args__-redefinition2": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.history.RunnableWithMessageHistory.__match_args__", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "name", "bound", "kwargs", "config", "config_factories", "custom_input_type", "custom_output_type", "get_session_history", "input_messages_key", "output_messages_key", "history_messages_key", "history_factory_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "name", "bound", "kwargs", "config", "config_factories", "custom_input_type", "custom_output_type", "get_session_history", "input_messages_key", "output_messages_key", "history_messages_key", "history_factory_config"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.GetSessionHistoryCallable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RunnableWithMessageHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "name", "bound", "kwargs", "config", "config_factories", "custom_input_type", "custom_output_type", "get_session_history", "input_messages_key", "output_messages_key", "history_messages_key", "history_factory_config"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.GetSessionHistoryCallable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RunnableWithMessageHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__mypy-replace-redefinition": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.history.RunnableWithMessageHistory.__mypy-replace", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__mypy-replace-redefinition2": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.history.RunnableWithMessageHistory.__mypy-replace", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "_aenter_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._aenter_history", "name": "_aenter_history", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_aenter_history of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_aexit_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._aexit_history", "name": "_aexit_history", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", "langsmith.run_trees.RunTree", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_aexit_history of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enter_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._enter_history", "name": "_enter_history", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_enter_history of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_exit_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._exit_history", "name": "_exit_history", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", "langsmith.run_trees.RunTree", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_exit_history of RunnableWithMessageHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_input_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._get_input_messages", "name": "_get_input_messages", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_val"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": ["builtins.str", "langchain_core.messages.base.BaseMessage", {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_input_messages of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_output_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._get_output_messages", "name": "_get_output_messages", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output_val"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": ["builtins.str", "langchain_core.messages.base.BaseMessage", {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_output_messages of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_history_chain": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._history_chain", "name": "_history_chain", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}}}, "_merge_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "configs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory._merge_configs", "name": "_merge_configs", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "configs"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_merge_configs of RunnableWithMessageHistory", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_specs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.config_specs", "name": "config_specs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "config_specs of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.config_specs", "name": "config_specs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "config_specs of RunnableWithMessageHistory", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_input_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.get_input_schema", "name": "get_input_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_input_schema of RunnableWithMessageHistory", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.get_input_schema", "name": "get_input_schema", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_input_schema of RunnableWithMessageHistory", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_output_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.get_output_schema", "name": "get_output_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_output_schema of RunnableWithMessageHistory", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.get_output_schema", "name": "get_output_schema", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["langchain_core.runnables.history.RunnableWithMessageHistory", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_output_schema of RunnableWithMessageHistory", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_session_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_settable_property", "is_ready"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.get_session_history", "name": "get_session_history", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.GetSessionHistoryCallable"}}}, "history_factory_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.history_factory_config", "name": "history_factory_config", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.utils.ConfigurableFieldSpec"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "history_messages_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.history_messages_key", "name": "history_messages_key", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "input_messages_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.input_messages_key", "name": "input_messages_key", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "output_messages_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.output_messages_key", "name": "output_messages_key", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.history.RunnableWithMessageHistory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.runnables.history.RunnableWithMessageHistory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.runnables.history.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.runnables.history.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.runnables.history.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.runnables.history.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.runnables.history.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.runnables.history.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_parameter_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["callable_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.runnables.history._get_parameter_names", "name": "_get_parameter_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["callable_"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.history.GetSessionHistoryCallable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_parameter_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "create_model_v2": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.pydantic.create_model_v2", "kind": "Gdef"}, "get_unique_config_specs": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.utils.get_unique_config_specs", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "load": {".class": "SymbolTableNode", "cross_ref": "langchain_core.load.load.load", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/history.py"}