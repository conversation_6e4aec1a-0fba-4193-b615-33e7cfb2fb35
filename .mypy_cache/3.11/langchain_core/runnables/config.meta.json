{"data_mtime": 1752086943, "dep_lines": [17, 25, 26, 8, 9, 3, 5, 6, 7, 10, 11, 12, 13, 15, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.runnables.utils", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "collections.abc", "concurrent.futures", "__future__", "asyncio", "uuid", "warnings", "contextlib", "<PERSON><PERSON><PERSON>", "functools", "typing", "typing_extensions", "builtins", "_contextvars", "_frozen_importlib", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "langchain_core.callbacks"], "hash": "ae6c300f299b0abca0a797fe419f3d9366798a2c", "id": "langchain_core.runnables.config", "ignore_all": true, "interface_hash": "ea734ae31c33ddff7e9a9f6208ac1933774ccb33", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/config.py", "plugin_data": null, "size": 20423, "suppressed": [], "version_id": "1.16.1"}