{"data_mtime": 1752086943, "dep_lines": [22, 29, 517, 561, 615, 25, 3, 5, 6, 7, 8, 9, 20, 27, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 20, 20, 20, 25, 5, 10, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.utils.pydantic", "langchain_core.runnables.base", "langchain_core.runnables.graph_ascii", "langchain_core.runnables.graph_png", "langchain_core.runnables.graph_mermaid", "collections.abc", "__future__", "inspect", "collections", "dataclasses", "enum", "typing", "uuid", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "d9f51aee34be4333df7153be87b78d02f965d27e", "id": "langchain_core.runnables.graph", "ignore_all": true, "interface_hash": "e61657912413f93bc80840feb2cc8b4c322c4c40", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/graph.py", "plugin_data": null, "size": 23386, "suppressed": [], "version_id": "1.16.1"}