{"data_mtime": 1752086943, "dep_lines": [18, 19, 28, 35, 38, 6, 3, 4, 5, 7, 15, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.runnables.base", "langchain_core.runnables.config", "langchain_core.runnables.utils", "langchain_core.utils.aiter", "langchain_core.callbacks.manager", "collections.abc", "asyncio", "inspect", "typing", "functools", "pydantic", "typing_extensions", "builtins", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "contextlib", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.utils", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.main", "types", "uuid"], "hash": "d34b5fafc80d21280d1efc8a668084aab2064492", "id": "langchain_core.runnables.fallbacks", "ignore_all": true, "interface_hash": "4dea9a6e9aff4b4cfb4c689b0a8ae7ca538e912a", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/fallbacks.py", "plugin_data": null, "size": 24334, "suppressed": [], "version_id": "1.16.1"}