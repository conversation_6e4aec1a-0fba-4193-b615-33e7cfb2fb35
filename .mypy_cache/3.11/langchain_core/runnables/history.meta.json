{"data_mtime": 1752086944, "dep_lines": [20, 21, 22, 23, 28, 31, 32, 33, 34, 6, 19, 379, 3, 5, 7, 8, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 25, 25, 25, 5, 5, 20, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.load.load", "langchain_core.runnables.base", "langchain_core.runnables.passthrough", "langchain_core.runnables.utils", "langchain_core.utils.pydantic", "langchain_core.language_models.base", "langchain_core.messages.base", "langchain_core.runnables.config", "langchain_core.tracers.schemas", "collections.abc", "langchain_core.chat_history", "langchain_core.messages", "__future__", "inspect", "types", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.prompt_values", "langsmith", "langsmith.run_trees", "langsmith.schemas", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "uuid"], "hash": "862dffdfebe0964ddc6d550f87c480090785c1d0", "id": "langchain_core.runnables.history", "ignore_all": true, "interface_hash": "995e546e718cb2a62237b218ca9eea673432000f", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/runnables/history.py", "plugin_data": null, "size": 25051, "suppressed": [], "version_id": "1.16.1"}