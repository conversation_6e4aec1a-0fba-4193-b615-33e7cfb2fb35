{"data_mtime": 1752086943, "dep_lines": [16, 10, 19, 20, 21, 3, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 25, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.tools.simple", "langchain_core.prompts", "langchain_core.callbacks", "langchain_core.documents", "langchain_core.retrievers", "__future__", "functools", "typing", "pydantic", "builtins", "_frozen_importlib", "abc", "annotated_types", "langchain_core.callbacks.base", "langchain_core.documents.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.prompts.base", "langchain_core.runnables", "langchain_core.runnables.base", "langchain_core.tools.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "re", "typing_extensions"], "hash": "04512b99fb40761977a7e64d67a99b73dbdf5544", "id": "langchain_core.tools.retriever", "ignore_all": true, "interface_hash": "8083489d9631e10878f3a22634592bbaeb0b7f84", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tools/retriever.py", "plugin_data": null, "size": 3873, "suppressed": [], "version_id": "1.16.1"}