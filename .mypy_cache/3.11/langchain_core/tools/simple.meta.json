{"data_mtime": 1752086943, "dep_lines": [22, 5, 17, 21, 30, 3, 6, 7, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.tools.base", "collections.abc", "langchain_core.callbacks", "langchain_core.runnables", "langchain_core.messages", "__future__", "inspect", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.tool", "langchain_core.runnables.base", "langchain_core.runnables.config", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.v1", "pydantic.v1.error_wrappers", "pydantic.v1.utils", "pydantic_core", "pydantic_core._pydantic_core", "uuid"], "hash": "a1fd53827f785a0d15ce269a5ff5428a266dc650", "id": "langchain_core.tools.simple", "ignore_all": true, "interface_hash": "d48fab3919e6d4ccc0dee3417b8166785a053d1e", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tools/simple.py", "plugin_data": null, "size": 6048, "suppressed": [], "version_id": "1.16.1"}