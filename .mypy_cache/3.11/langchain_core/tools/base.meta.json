{"data_mtime": 1752086943, "dep_lines": [49, 57, 58, 59, 63, 37, 42, 43, 50, 74, 3, 5, 6, 7, 8, 9, 10, 27, 40, 73, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 10, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.messages.tool", "langchain_core.runnables.config", "langchain_core.runnables.utils", "langchain_core.utils.function_calling", "langchain_core.utils.pydantic", "pydantic.v1", "langchain_core._api", "langchain_core.callbacks", "langchain_core.runnables", "collections.abc", "__future__", "functools", "inspect", "json", "typing", "warnings", "abc", "pydantic", "typing_extensions", "uuid", "builtins", "_frozen_importlib", "annotated_types", "langchain_core._api.deprecation", "langchain_core.callbacks.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic.v1.error_wrappers", "pydantic.v1.utils", "pydantic_core", "pydantic_core._pydantic_core", "pydantic_core.core_schema", "re"], "hash": "db14e48816151099f5dd3f0869a6a8998f7d3562", "id": "langchain_core.tools.base", "ignore_all": true, "interface_hash": "0a155627e0021f76c61850da60f43f5e4ac405ba", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tools/base.py", "plugin_data": null, "size": 49935, "suppressed": [], "version_id": "1.16.1"}