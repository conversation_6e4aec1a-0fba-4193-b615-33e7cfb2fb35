{"data_mtime": 1752086943, "dep_lines": [35, 36, 37, 38, 39, 40, 41, 42, 49, 17, 33, 34, 47, 48, 10, 12, 13, 14, 15, 16, 18, 19, 31, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 25, 25, 5, 10, 10, 10, 10, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.messages.ai", "langchain_core.messages.base", "langchain_core.messages.chat", "langchain_core.messages.function", "langchain_core.messages.human", "langchain_core.messages.modifier", "langchain_core.messages.system", "langchain_core.messages.tool", "langchain_core.runnables.base", "collections.abc", "langchain_core.exceptions", "langchain_core.messages", "langchain_core.language_models", "langchain_core.prompt_values", "__future__", "base64", "inspect", "json", "logging", "math", "functools", "typing", "pydantic", "langchain_text_splitters", "builtins", "_collections_abc", "_frozen_importlib", "abc", "langchain_core.documents", "langchain_core.documents.transformers", "langchain_core.language_models.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables", "langchain_text_splitters.base", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "69966e6c0e9cc6887ec7eb1bc7efe685a235ebe9", "id": "langchain_core.messages.utils", "ignore_all": true, "interface_hash": "0423e0ba4856ce09e187a7ea56892e6db6910106", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/messages/utils.py", "plugin_data": null, "size": 67469, "suppressed": [], "version_id": "1.16.1"}