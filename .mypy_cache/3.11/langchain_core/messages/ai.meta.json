{"data_mtime": 1752086943, "dep_lines": [11, 16, 32, 33, 34, 3, 4, 5, 6, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.messages.base", "langchain_core.messages.tool", "langchain_core.utils._merge", "langchain_core.utils.json", "langchain_core.utils.usage", "json", "logging", "operator", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "json.encoder", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.utils", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._model_construction", "pydantic.functional_validators", "pydantic.main", "pydantic_core", "pydantic_core.core_schema", "types"], "hash": "4adb9af1127f8591ea1705c60abb2e85677b7e5c", "id": "langchain_core.messages.ai", "ignore_all": true, "interface_hash": "c23b6601abefd18f81366b4fca573a279a1ca6dc", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/messages/ai.py", "plugin_data": null, "size": 17920, "suppressed": [], "version_id": "1.16.1"}