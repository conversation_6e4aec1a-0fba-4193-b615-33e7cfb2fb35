{".class": "MypyFile", "_fullname": "langchain_core.messages.utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AIMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.ai.AIMessage", "kind": "Gdef"}, "AIMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.ai.AIMessageChunk", "kind": "Gdef"}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_core.messages.utils.AnyMessage", "line": 67, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["langchain_core.messages.ai.AIMessage", "langchain_core.messages.human.HumanMessage", "langchain_core.messages.chat.ChatMessage", "langchain_core.messages.system.SystemMessage", "langchain_core.messages.function.FunctionMessage", "langchain_core.messages.tool.ToolMessage", "langchain_core.messages.ai.AIMessageChunk", "langchain_core.messages.human.HumanMessageChunk", "langchain_core.messages.chat.ChatMessageChunk", "langchain_core.messages.system.SystemMessageChunk", "langchain_core.messages.function.FunctionMessageChunk", "langchain_core.messages.tool.ToolMessageChunk"], "uses_pep604_syntax": false}}}, "BaseLanguageModel": {".class": "SymbolTableNode", "cross_ref": "langchain_core.language_models.base.BaseLanguageModel", "kind": "Gdef"}, "BaseMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.base.BaseMessage", "kind": "Gdef"}, "BaseMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.base.BaseMessageChunk", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChatMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.chat.ChatMessage", "kind": "Gdef"}, "ChatMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.chat.ChatMessageChunk", "kind": "Gdef"}, "Discriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.types.Discriminator", "kind": "Gdef"}, "ErrorCode": {".class": "SymbolTableNode", "cross_ref": "langchain_core.exceptions.ErrorCode", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef"}, "FunctionMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.function.FunctionMessage", "kind": "Gdef"}, "FunctionMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.function.FunctionMessageChunk", "kind": "Gdef"}, "HumanMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.human.HumanMessage", "kind": "Gdef"}, "HumanMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.human.HumanMessageChunk", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MessageLikeRepresentation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "langchain_core.messages.utils.MessageLikeRepresentation", "line": 204, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["langchain_core.messages.base.BaseMessage", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PromptValue": {".class": "SymbolTableNode", "cross_ref": "langchain_core.prompt_values.PromptValue", "kind": "Gdef"}, "RemoveMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.modifier.RemoveMessage", "kind": "Gdef"}, "Runnable": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.Runnable", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SystemMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.system.SystemMessage", "kind": "Gdef"}, "SystemMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.system.SystemMessageChunk", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tag": {".class": "SymbolTableNode", "cross_ref": "pydantic.types.Tag", "kind": "Gdef"}, "TextSplitter": {".class": "SymbolTableNode", "cross_ref": "langchain_text_splitters.base.TextSplitter", "kind": "Gdef"}, "ToolCall": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.tool.ToolCall", "kind": "Gdef"}, "ToolMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.tool.ToolMessage", "kind": "Gdef"}, "ToolMessageChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.tool.ToolMessageChunk", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CHUNK_MSG_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.messages.utils._CHUNK_MSG_MAP", "name": "_CHUNK_MSG_MAP", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "langchain_core.messages.base.BaseMessageChunk"}, {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_MSG_CHUNK_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.messages.utils._MSG_CHUNK_MAP", "name": "_MSG_CHUNK_MAP", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessageChunk"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.messages.utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.messages.utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.messages.utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.messages.utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.messages.utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.messages.utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_bytes_to_b64_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bytes_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._bytes_to_b64_str", "name": "_bytes_to_b64_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bytes_"], "arg_types": ["builtins.bytes"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_bytes_to_b64_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_chunk_to_msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._chunk_to_msg", "name": "_chunk_to_msg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chunk"], "arg_types": ["langchain_core.messages.base.BaseMessageChunk"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_chunk_to_msg", "ret_type": "langchain_core.messages.base.BaseMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_to_message": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._convert_to_message", "name": "_convert_to_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_to_message", "ret_type": "langchain_core.messages.base.BaseMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_to_openai_tool_calls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tool_calls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._convert_to_openai_tool_calls", "name": "_convert_to_openai_tool_calls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tool_calls"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.tool.ToolCall"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_to_openai_tool_calls", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_message_from_message_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["message_type", "content", "name", "tool_call_id", "tool_calls", "id", "additional_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._create_message_from_message_type", "name": "_create_message_from_message_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["message_type", "content", "name", "tool_call_id", "tool_calls", "id", "additional_kwargs"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_message_from_message_type", "ret_type": "langchain_core.messages.base.BaseMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_text_splitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._default_text_splitter", "name": "_default_text_splitter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_default_text_splitter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_first_max_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5], "arg_names": ["messages", "max_tokens", "token_counter", "text_splitter", "partial_strategy", "end_on"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._first_max_tokens", "name": "_first_max_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5], "arg_names": ["messages", "max_tokens", "token_counter", "text_splitter", "partial_strategy", "end_on"], "arg_types": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "first"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_first_max_tokens", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_message_openai_role": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._get_message_openai_role", "name": "_get_message_openai_role", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": ["langchain_core.messages.base.BaseMessage"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_message_openai_role", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._get_type", "name": "_get_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["v"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_message_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["message", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._is_message_type", "name": "_is_message_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["message", "type_"], "arg_types": ["langchain_core.messages.base.BaseMessage", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_message_type", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_max_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5], "arg_names": ["messages", "max_tokens", "token_counter", "text_splitter", "allow_partial", "include_system", "start_on", "end_on"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._last_max_tokens", "name": "_last_max_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5], "arg_names": ["messages", "max_tokens", "token_counter", "text_splitter", "allow_partial", "include_system", "start_on", "end_on"], "arg_types": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_last_max_tokens", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_message_from_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._message_from_dict", "name": "_message_from_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_message_from_dict", "ret_type": "langchain_core.messages.base.BaseMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_msg_to_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._msg_to_chunk", "name": "_msg_to_chunk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["message"], "arg_types": ["langchain_core.messages.base.BaseMessage"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_msg_to_chunk", "ret_type": "langchain_core.messages.base.BaseMessageChunk", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_runnable_support": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils._runnable_support", "name": "_runnable_support", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_runnable_support", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "trim_messages", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "convert_to_messages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["messages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils.convert_to_messages", "name": "convert_to_messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["messages"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "langchain_core.prompt_values.PromptValue"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_messages", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_to_openai_data_block": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.content_blocks.convert_to_openai_data_block", "kind": "Gdef"}, "convert_to_openai_messages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["messages", "text_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils.convert_to_openai_messages", "name": "convert_to_openai_messages", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["messages", "text_format"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "string"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_openai_messages", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count_tokens_approximately": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["messages", "chars_per_token", "extra_tokens_per_message", "count_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils.count_tokens_approximately", "name": "count_tokens_approximately", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["messages", "chars_per_token", "extra_tokens_per_message", "count_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.float", "builtins.float", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_tokens_approximately", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_message": {".class": "SymbolTableNode", "cross_ref": "langchain_core.exceptions.create_message", "kind": "Gdef"}, "filter_messages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "include_names", "exclude_names", "include_types", "exclude_types", "include_ids", "exclude_ids", "exclude_tool_calls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.messages.utils.filter_messages", "name": "filter_messages", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "include_names", "exclude_names", "include_types", "exclude_types", "include_ids", "exclude_ids", "exclude_tool_calls"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "langchain_core.prompt_values.PromptValue"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "filter_messages", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.messages.utils.filter_messages", "name": "filter_messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "filter_messages", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_buffer_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["messages", "human_prefix", "ai_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils.get_buffer_string", "name": "get_buffer_string", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["messages", "human_prefix", "ai_prefix"], "arg_types": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_buffer_string", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_data_content_block": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.content_blocks.is_data_content_block", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.messages.utils.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "merge_message_runs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["messages", "chunk_separator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.messages.utils.merge_message_runs", "name": "merge_message_runs", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["messages", "chunk_separator"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "langchain_core.prompt_values.PromptValue"], "uses_pep604_syntax": false}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge_message_runs", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.messages.utils.merge_message_runs", "name": "merge_message_runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "merge_message_runs", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "message_chunk_to_message": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils.message_chunk_to_message", "name": "message_chunk_to_message", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chunk"], "arg_types": ["langchain_core.messages.base.BaseMessageChunk"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "message_chunk_to_message", "ret_type": "langchain_core.messages.base.BaseMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "messages_from_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["messages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.messages.utils.messages_from_dict", "name": "messages_from_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["messages"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "messages_from_dict", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "trim_messages": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "max_tokens", "token_counter", "strategy", "allow_partial", "end_on", "start_on", "include_system", "text_splitter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.messages.utils.trim_messages", "name": "trim_messages", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["messages", "max_tokens", "token_counter", "strategy", "allow_partial", "end_on", "start_on", "include_system", "text_splitter"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.messages.utils.MessageLikeRepresentation"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "langchain_core.prompt_values.PromptValue"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.messages.base.BaseMessage"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "langchain_core.language_models.base.BaseLanguageModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "first"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "langchain_core.messages.base.BaseMessage"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "langchain_text_splitters.base.TextSplitter", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "trim_messages", "ret_type": {".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.messages.utils.trim_messages", "name": "trim_messages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": "trim_messages", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/messages/utils.py"}