{"data_mtime": 1752086943, "dep_lines": [9, 11, 12, 17, 10, 15, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 5, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.load.serializable", "langchain_core.utils._merge", "langchain_core.utils.interactive_env", "langchain_core.prompts.chat", "langchain_core.utils", "collections.abc", "__future__", "typing", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "langchain_core.load", "langchain_core.prompts", "langchain_core.prompts.base", "langchain_core.runnables", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.main", "pydantic.types", "re", "typing_extensions"], "hash": "0c064ebc8b0447de2adb9ddab3a1f2a6590b17aa", "id": "langchain_core.messages.base", "ignore_all": true, "interface_hash": "fd61702ba23113efea61edc7127a4f0f7ce9b8a3", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/messages/base.py", "plugin_data": null, "size": 9392, "suppressed": [], "version_id": "1.16.1"}