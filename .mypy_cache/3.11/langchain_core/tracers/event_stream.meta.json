{"data_mtime": 1752086943, "dep_lines": [20, 27, 33, 38, 39, 40, 47, 21, 22, 43, 45, 46, 3, 5, 6, 7, 8, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 5, 5, 25, 25, 20, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.callbacks.base", "langchain_core.runnables.schema", "langchain_core.runnables.utils", "langchain_core.tracers._streaming", "langchain_core.tracers.memory_stream", "langchain_core.utils.aiter", "langchain_core.tracers.log_stream", "langchain_core.messages", "langchain_core.outputs", "collections.abc", "langchain_core.documents", "langchain_core.runnables", "__future__", "asyncio", "contextlib", "logging", "typing", "uuid", "typing_extensions", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.events", "asyncio.exceptions", "asyncio.tasks", "langchain_core.callbacks", "langchain_core.documents.base", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.outputs.chat_generation", "langchain_core.outputs.generation", "langchain_core.outputs.llm_result", "langchain_core.runnables.base", "langchain_core.runnables.config", "langchain_core.tracers.base", "langchain_core.tracers.core", "langchain_core.utils", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "types"], "hash": "5a05c76e0aa645d0cd89d33833848262fc5cc4e6", "id": "langchain_core.tracers.event_stream", "ignore_all": true, "interface_hash": "aace8b80a15fee2f1ebc69c624c74c847bece189", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/event_stream.py", "plugin_data": null, "size": 33581, "suppressed": [], "version_id": "1.16.1"}