{"data_mtime": 1752086943, "dep_lines": [24, 27, 28, 29, 35, 36, 623, 23, 25, 26, 32, 3, 5, 6, 7, 8, 9, 10, 21, 33, 124, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 20], "dep_prios": [5, 5, 5, 5, 25, 25, 20, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 5, 5, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["langchain_core.load.load", "langchain_core.tracers._streaming", "langchain_core.tracers.base", "langchain_core.tracers.memory_stream", "langchain_core.runnables.utils", "langchain_core.tracers.schemas", "langchain_core.callbacks.base", "langchain_core.load", "langchain_core.outputs", "langchain_core.runnables", "collections.abc", "__future__", "asyncio", "contextlib", "copy", "threading", "collections", "typing", "typing_extensions", "uuid", "pprint", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_thread", "_typeshed", "abc", "asyncio.events", "asyncio.exceptions", "asyncio.tasks", "datetime", "langchain_core.callbacks", "langchain_core.load.serializable", "langchain_core.outputs.chat_generation", "langchain_core.outputs.generation", "langchain_core.runnables.base", "langchain_core.runnables.config", "langchain_core.tracers.core", "langsmith", "langsmith.run_trees", "langsmith.schemas", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "types"], "hash": "5b27e0174745157328ce9b23efb08bf66ee89efc", "id": "langchain_core.tracers.log_stream", "ignore_all": true, "interface_hash": "fd86c5e380aedbd12aea2c4e1dfbc24f2c60e499", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/log_stream.py", "plugin_data": null, "size": 24015, "suppressed": ["jsonpatch"], "version_id": "1.16.1"}