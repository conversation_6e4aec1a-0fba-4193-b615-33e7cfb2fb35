{".class": "MypyFile", "_fullname": "langchain_core.tracers.evaluation", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseTracer": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.base.BaseTracer", "kind": "Gdef"}, "EvaluationResult": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation.evaluator.EvaluationResult", "kind": "Gdef"}, "EvaluationResults": {".class": "SymbolTableNode", "cross_ref": "langsmith.evaluation.evaluator.EvaluationResults", "kind": "Gdef"}, "EvaluatorCallbackHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.tracers.base.BaseTracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler", "name": "EvaluatorCallbackHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_core.tracers.evaluation", "mro": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler", "langchain_core.tracers.base.BaseTracer", "langchain_core.tracers.core._TracerCore", "langchain_core.callbacks.base.BaseCallbackHandler", "abc.ABC", "langchain_core.callbacks.base.LLMManagerMixin", "langchain_core.callbacks.base.ChainManagerMixin", "langchain_core.callbacks.base.ToolManagerMixin", "langchain_core.callbacks.base.RetrieverManagerMixin", "langchain_core.callbacks.base.CallbackManagerMixin", "langchain_core.callbacks.base.RunManagerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "evaluators", "client", "example_id", "skip_unfinished", "project_name", "max_concurrency", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "evaluators", "client", "example_id", "skip_unfinished", "project_name", "max_concurrency", "kwargs"], "arg_types": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler", {".class": "Instance", "args": ["langsmith.evaluation.evaluator.RunEvaluator"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["langsmith.client.Client", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["uuid.UUID", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of EvaluatorCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evaluate_in_project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "evaluator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler._evaluate_in_project", "name": "_evaluate_in_project", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "run", "evaluator"], "arg_types": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler", "langsmith.run_trees.RunTree", "langsmith.evaluation.evaluator.RunEvaluator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_evaluate_in_project of EvaluatorCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_evaluation_feedback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "evaluator_response", "run", "source_run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler._log_evaluation_feedback", "name": "_log_evaluation_feedback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "evaluator_response", "run", "source_run_id"], "arg_types": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler", {".class": "UnionType", "items": ["langsmith.evaluation.evaluator.EvaluationResult", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.EvaluationResults"}], "uses_pep604_syntax": false}, "langsmith.run_trees.RunTree", {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_log_evaluation_feedback of EvaluatorCallbackHandler", "ret_type": {".class": "Instance", "args": ["langsmith.evaluation.evaluator.EvaluationResult"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_persist_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler._persist_run", "name": "_persist_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_persist_run of EvaluatorCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_select_eval_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler._select_eval_results", "name": "_select_eval_results", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "results"], "arg_types": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler", {".class": "UnionType", "items": ["langsmith.evaluation.evaluator.EvaluationResult", {".class": "TypeAliasType", "args": [], "type_ref": "langsmith.evaluation.evaluator.EvaluationResults"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_select_eval_results of EvaluatorCallbackHandler", "ret_type": {".class": "Instance", "args": ["langsmith.evaluation.evaluator.EvaluationResult"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.client", "name": "client", "setter_type": null, "type": "langsmith.client.Client"}}, "evaluators": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.evaluators", "name": "evaluators", "setter_type": null, "type": {".class": "Instance", "args": ["langsmith.evaluation.evaluator.RunEvaluator"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "example_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.example_id", "name": "example_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "executor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.executor", "name": "executor", "setter_type": null, "type": {".class": "UnionType", "items": ["concurrent.futures.thread.ThreadPoolExecutor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "futures": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.futures", "name": "futures", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "extra_attrs": null, "type_ref": "_weakrefset.WeakSet"}}}, "lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.lock", "name": "lock", "setter_type": null, "type": "_thread.LockType"}}, "logged_eval_results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.logged_eval_results", "name": "logged_eval_results", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["langsmith.evaluation.evaluator.EvaluationResult"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "project_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.project_name", "name": "project_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "skip_unfinished": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.skip_unfinished", "name": "skip_unfinished", "setter_type": null, "type": "builtins.bool"}}, "wait_for_futures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.wait_for_futures", "name": "wait_for_futures", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_for_futures of EvaluatorCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.evaluation.EvaluatorCallbackHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Future": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Future", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Run": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.schemas.Run", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_TRACERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.evaluation._TRACERS", "name": "_TRACERS", "setter_type": null, "type": {".class": "Instance", "args": ["langchain_core.tracers.evaluation.EvaluatorCallbackHandler"], "extra_attrs": null, "type_ref": "_weakrefset.WeakSet"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.evaluation.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.evaluation.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.evaluation.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.evaluation.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.evaluation.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.evaluation.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_executor": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.langchain._get_executor", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "langchain_tracer": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.langchain", "kind": "Gdef"}, "langsmith": {".class": "SymbolTableNode", "cross_ref": "langsmith", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.tracers.evaluation.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "tracing_v2_enabled": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.context.tracing_v2_enabled", "kind": "Gdef"}, "wait": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.wait", "kind": "Gdef"}, "wait_for_all_evaluators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.evaluation.wait_for_all_evaluators", "name": "wait_for_all_evaluators", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_for_all_evaluators", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/evaluation.py"}