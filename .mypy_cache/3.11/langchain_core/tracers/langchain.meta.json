{"data_mtime": 1752086943, "dep_lines": [24, 25, 6, 12, 13, 22, 23, 28, 29, 3, 5, 7, 8, 9, 11, 14, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 25, 25, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.tracers.base", "langchain_core.tracers.schemas", "concurrent.futures", "langsmith.run_trees", "langsmith.utils", "langchain_core.env", "langchain_core.load", "langchain_core.messages", "langchain_core.outputs", "__future__", "logging", "datetime", "typing", "uuid", "langsmith", "tenacity", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "enum", "functools", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.outputs.chat_generation", "langchain_core.outputs.generation", "langchain_core.tracers.core", "langsmith.client", "langsmith.schemas", "os", "pathlib", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "types"], "hash": "5a823ec888600a25cf1a2c8f7f69fb24d85ec574", "id": "langchain_core.tracers.langchain", "ignore_all": true, "interface_hash": "29713070908b056530269509e3e95b32231748df", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/langchain.py", "plugin_data": null, "size": 10395, "suppressed": [], "version_id": "1.16.1"}