{".class": "MypyFile", "_fullname": "langchain_core.tracers.log_stream", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "BaseTracer": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.base.BaseTracer", "kind": "Gdef"}, "ChatGenerationChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.outputs.chat_generation.ChatGenerationChunk", "kind": "Gdef"}, "GenerationChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.outputs.generation.GenerationChunk", "kind": "Gdef"}, "Input": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.utils.Input", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "LogEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.log_stream.LogEntry", "name": "LogEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream.LogEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_core.tracers.log_stream", "mro": ["langchain_core.tracers.log_stream.LogEntry", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["id", "builtins.str"], ["name", "builtins.str"], ["type", "builtins.str"], ["tags", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["metadata", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["start_time", "builtins.str"], ["streamed_output_str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["streamed_output", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], ["inputs", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["final_output", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_time", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["end_time", "final_output", "id", "metadata", "name", "start_time", "streamed_output", "streamed_output_str", "tags", "type"]}}}, "LogStreamCallbackHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.tracers.base.BaseTracer", "langchain_core.tracers._streaming._StreamingCallbackHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler", "name": "LogStreamCallbackHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_core.tracers.log_stream", "mro": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "langchain_core.tracers.base.BaseTracer", "langchain_core.tracers.core._TracerCore", "langchain_core.callbacks.base.BaseCallbackHandler", "langchain_core.tracers._streaming._StreamingCallbackHandler", "abc.ABC", "langchain_core.callbacks.base.LLMManagerMixin", "langchain_core.callbacks.base.ChainManagerMixin", "langchain_core.callbacks.base.ToolManagerMixin", "langchain_core.callbacks.base.RetrieverManagerMixin", "langchain_core.callbacks.base.CallbackManagerMixin", "langchain_core.callbacks.base.RunManagerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of LogStreamCallbackHandler", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "auto_close", "include_names", "include_types", "include_tags", "exclude_names", "exclude_types", "exclude_tags", "_schema_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "auto_close", "include_names", "include_types", "include_tags", "exclude_names", "exclude_types", "exclude_tags", "_schema_format"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "original"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "streaming_events"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LogStreamCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_counter_map_by_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler._counter_map_by_name", "name": "_counter_map_by_name", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_key_map_by_run_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler._key_map_by_run_id", "name": "_key_map_by_run_id", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_on_llm_new_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "run", "token", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler._on_llm_new_token", "name": "_on_llm_new_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "run", "token", "chunk"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "langsmith.run_trees.RunTree", "builtins.str", {".class": "UnionType", "items": ["langchain_core.outputs.generation.GenerationChunk", "langchain_core.outputs.chat_generation.ChatGenerationChunk", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_new_token of LogStreamCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_run_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler._on_run_create", "name": "_on_run_create", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_run_create of LogStreamCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_run_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler._on_run_update", "name": "_on_run_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_run_update of LogStreamCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_persist_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler._persist_run", "name": "_persist_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_persist_run of LogStreamCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_close": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.auto_close", "name": "auto_close", "setter_type": null, "type": "builtins.bool"}}, "exclude_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.exclude_names", "name": "exclude_names", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "exclude_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.exclude_tags", "name": "exclude_tags", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "exclude_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.exclude_types", "name": "exclude_types", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "include_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.include_names", "name": "include_names", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "include_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.include_run", "name": "include_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "include_run of LogStreamCallbackHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "include_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.include_tags", "name": "include_tags", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "include_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.include_types", "name": "include_types", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.lock", "name": "lock", "setter_type": null, "type": "_thread.LockType"}}, "receive_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.receive_stream", "name": "receive_stream", "setter_type": null, "type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "langchain_core.tracers.memory_stream._ReceiveStream"}}}, "root_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.root_id", "name": "root_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "ops"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of LogStreamCallbackHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.send_stream", "name": "send_stream", "setter_type": null, "type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "langchain_core.tracers.memory_stream._SendStream"}}}, "tap_output_aiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "run_id", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_aiter", "name": "tap_output_aiter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "run_id", "output"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "uuid.UUID", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "id": -1, "name": "T", "namespace": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_aiter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tap_output_aiter of LogStreamCallbackHandler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "id": -1, "name": "T", "namespace": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_aiter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "id": -1, "name": "T", "namespace": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_aiter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "tap_output_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "run_id", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_iter", "name": "tap_output_iter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "run_id", "output"], "arg_types": ["langchain_core.tracers.log_stream.LogStreamCallbackHandler", "uuid.UUID", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "id": -1, "name": "T", "namespace": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_iter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tap_output_iter of LogStreamCallbackHandler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "id": -1, "name": "T", "namespace": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_iter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "id": -1, "name": "T", "namespace": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.tap_output_iter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.LogStreamCallbackHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.log_stream.LogStreamCallbackHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Output": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.utils.Output", "kind": "Gdef"}, "Run": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.schemas.Run", "kind": "Gdef"}, "RunLog": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.tracers.log_stream.RunLogPatch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.log_stream.RunLog", "name": "RunLog", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream.RunLog", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_core.tracers.log_stream", "mro": ["langchain_core.tracers.log_stream.RunLog", "langchain_core.tracers.log_stream.RunLogPatch", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLog.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.tracers.log_stream.RunLog", {".class": "UnionType", "items": ["langchain_core.tracers.log_stream.RunLogPatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__add__ of RunLog", "ret_type": "langchain_core.tracers.log_stream.RunLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLog.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.tracers.log_stream.RunLog", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of RunLog", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.RunLog.__eq__", "name": "__eq__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.tracers.log_stream.RunLog", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of RunLog", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3], "arg_names": ["self", "ops", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLog.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3], "arg_names": ["self", "ops", "state"], "arg_types": ["langchain_core.tracers.log_stream.RunLog", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.tracers.log_stream.RunState"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RunLog", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLog.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.tracers.log_stream.RunLog"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of RunLog", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.RunLog.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.tracers.log_stream.RunLog"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of RunLog", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langchain_core.tracers.log_stream.RunLog.state", "name": "state", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.tracers.log_stream.RunState"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.RunLog.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.log_stream.RunLog", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RunLogPatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.log_stream.RunLogPatch", "name": "RunLogPatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream.RunLogPatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_core.tracers.log_stream", "mro": ["langchain_core.tracers.log_stream.RunLogPatch", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.tracers.log_stream.RunLogPatch", {".class": "UnionType", "items": ["langchain_core.tracers.log_stream.RunLogPatch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__add__ of RunLogPatch", "ret_type": "langchain_core.tracers.log_stream.RunLog", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.tracers.log_stream.RunLogPatch", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of RunLogPatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.__eq__", "name": "__eq__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.tracers.log_stream.RunLogPatch", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of RunLogPatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "ops"], "arg_types": ["langchain_core.tracers.log_stream.RunLogPatch", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RunLogPatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.tracers.log_stream.RunLogPatch"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of RunLogPatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.tracers.log_stream.RunLogPatch"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of RunLogPatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "langchain_core.tracers.log_stream.RunLogPatch.ops", "name": "ops", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.RunLogPatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.log_stream.RunLogPatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RunState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.log_stream.RunState", "name": "RunState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream.RunState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "langchain_core.tracers.log_stream", "mro": ["langchain_core.tracers.log_stream.RunState", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["id", "builtins.str"], ["streamed_output", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], ["final_output", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["name", "builtins.str"], ["type", "builtins.str"], ["logs", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.tracers.log_stream.LogEntry"}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["final_output", "id", "logs", "name", "streamed_output", "type"]}}}, "Runnable": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.base.Runnable", "kind": "Gdef"}, "RunnableConfig": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.config.RunnableConfig", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.log_stream.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_MemoryStream": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.memory_stream._MemoryStream", "kind": "Gdef"}, "_StreamingCallbackHandler": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers._streaming._StreamingCallbackHandler", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.log_stream.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.log_stream.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.log_stream.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.log_stream.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.log_stream.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.log_stream.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_astream_log_implementation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream._astream_log_implementation", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3, 5, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_generator", "is_coroutine", "is_async_generator"], "fullname": "langchain_core.tracers.log_stream._astream_log_implementation", "name": "_astream_log_implementation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLog"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3, 5, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "langchain_core.tracers.log_stream._astream_log_implementation", "name": "_astream_log_implementation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream._astream_log_implementation", "name": "_astream_log_implementation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 3, 3, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "langchain_core.tracers.log_stream._astream_log_implementation", "name": "_astream_log_implementation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 3, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLog"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream._astream_log_implementation", "name": "_astream_log_implementation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 3, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLog"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 5, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLogPatch"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#0", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 3, 5, 4], "arg_names": ["runnable", "value", "config", "stream", "diff", "with_streamed_output_list", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "langchain_core.runnables.base.Runnable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.runnables.config.RunnableConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "langchain_core.tracers.log_stream.LogStreamCallbackHandler", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_astream_log_implementation", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.log_stream.RunLog"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Input", "id": -1, "name": "Input", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.runnables.utils.Output", "id": -2, "name": "Output", "namespace": "langchain_core.tracers.log_stream._astream_log_implementation#1", "upper_bound": "builtins.object", "values": [], "variance": 1}]}]}}}, "_get_standardized_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["run", "schema_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream._get_standardized_inputs", "name": "_get_standardized_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["run", "schema_format"], "arg_types": ["langsmith.run_trees.RunTree", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "original"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "streaming_events"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_standardized_inputs", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_standardized_outputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["run", "schema_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.log_stream._get_standardized_outputs", "name": "_get_standardized_outputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["run", "schema_format"], "arg_types": ["langsmith.run_trees.RunTree", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "original"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "streaming_events"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "original+chat"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_standardized_outputs", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "dumps": {".class": "SymbolTableNode", "cross_ref": "langchain_core.load.dump.dumps", "kind": "Gdef"}, "ensure_config": {".class": "SymbolTableNode", "cross_ref": "langchain_core.runnables.config.ensure_config", "kind": "Gdef"}, "jsonpatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.log_stream.jsonpatch", "name": "jsonpatch", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "langchain_core.tracers.log_stream.jsonpatch", "source_any": null, "type_of_any": 3}}}, "load": {".class": "SymbolTableNode", "cross_ref": "langchain_core.load.load.load", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/log_stream.py"}