{"data_mtime": 1752086943, "dep_lines": [27, 19, 20, 21, 30, 35, 36, 3, 5, 6, 7, 8, 9, 10, 31, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 5, 10, 10, 10, 5, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.tracers.schemas", "langchain_core.exceptions", "langchain_core.load", "langchain_core.outputs", "collections.abc", "langchain_core.documents", "langchain_core.messages", "__future__", "logging", "sys", "traceback", "abc", "datetime", "typing", "uuid", "tenacity", "builtins", "_frozen_importlib", "_typeshed", "langchain_core.documents.base", "langchain_core.load.dump", "langchain_core.load.serializable", "langchain_core.messages.base", "langchain_core.outputs.chat_generation", "langchain_core.outputs.generation", "langchain_core.outputs.llm_result", "langsmith", "langsmith.run_trees", "langsmith.schemas", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "types", "typing_extensions"], "hash": "55e197a084fd538c404c379af4d2c795c6803810", "id": "langchain_core.tracers.core", "ignore_all": true, "interface_hash": "051afc9d13a74a4172cbd9f708ac4d3a00cb41e6", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/core.py", "plugin_data": null, "size": 22740, "suppressed": [], "version_id": "1.16.1"}