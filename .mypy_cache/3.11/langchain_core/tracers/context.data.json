{".class": "MypyFile", "_fullname": "langchain_core.tracers.context", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncCallbackManager": {".class": "SymbolTableNode", "cross_ref": "langchain_core.callbacks.manager.AsyncCallbackManager", "kind": "Gdef"}, "BaseCallbackHandler": {".class": "SymbolTableNode", "cross_ref": "langchain_core.callbacks.base.BaseCallbackHandler", "kind": "Gdef"}, "CallbackManager": {".class": "SymbolTableNode", "cross_ref": "langchain_core.callbacks.manager.CallbackManager", "kind": "Gdef"}, "Callbacks": {".class": "SymbolTableNode", "cross_ref": "langchain_core.callbacks.base.Callbacks", "kind": "Gdef"}, "ContextVar": {".class": "SymbolTableNode", "cross_ref": "_contextvars.ContextVar", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "LangChainTracer": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.langchain.LangChainTracer", "kind": "Gdef"}, "LangSmithClient": {".class": "SymbolTableNode", "cross_ref": "langsmith.client.Client", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RunCollectorCallbackHandler": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.run_collector.RunCollectorCallbackHandler", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracerSessionV1": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.schemas.TracerSessionV1", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.context.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.context.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.context.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.context.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.context.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.context.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_configure_hooks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.context._configure_hooks", "name": "_configure_hooks", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["langchain_core.callbacks.base.BaseCallbackHandler", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeType", "item": "langchain_core.callbacks.base.BaseCallbackHandler"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_get_trace_callbacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1], "arg_names": ["project_name", "example_id", "callback_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.context._get_trace_callbacks", "name": "_get_trace_callbacks", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["project_name", "example_id", "callback_manager"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langchain_core.callbacks.manager.CallbackManager", "langchain_core.callbacks.manager.AsyncCallbackManager", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_trace_callbacks", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.callbacks.base.Callbacks"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tracer_project": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.context._get_tracer_project", "name": "_get_tracer_project", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_tracer_project", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tracing_v2_is_enabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.context._tracing_v2_is_enabled", "name": "_tracing_v2_is_enabled", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_tracing_v2_is_enabled", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "local"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "collect_runs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.tracers.context.collect_runs", "name": "collect_runs", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_runs", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.run_collector.RunCollectorCallbackHandler", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.tracers.context.collect_runs", "name": "collect_runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_runs", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.run_collector.RunCollectorCallbackHandler", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "ls_rh": {".class": "SymbolTableNode", "cross_ref": "langsmith.run_helpers", "kind": "Gdef"}, "ls_utils": {".class": "SymbolTableNode", "cross_ref": "langsmith.utils", "kind": "Gdef"}, "register_configure_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["context_var", "inheritable", "handle_class", "env_var"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.context.register_configure_hook", "name": "register_configure_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["context_var", "inheritable", "handle_class", "env_var"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeType", "item": "langchain_core.callbacks.base.BaseCallbackHandler"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_configure_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_collector_var": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.context.run_collector_var", "name": "run_collector_var", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["langchain_core.tracers.run_collector.RunCollectorCallbackHandler", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}}}, "tracing_callback_var": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.context.tracing_callback_var", "name": "tracing_callback_var", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "tracing_enabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["session_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.tracers.context.tracing_enabled", "name": "tracing_enabled", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["session_name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tracing_enabled", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.schemas.TracerSessionV1", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.tracers.context.tracing_enabled", "name": "tracing_enabled", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["session_name"], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tracing_enabled", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.schemas.TracerSessionV1", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tracing_v2_callback_var": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.context.tracing_v2_callback_var", "name": "tracing_v2_callback_var", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["langchain_core.tracers.langchain.LangChainTracer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "_contextvars.ContextVar"}}}, "tracing_v2_enabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5], "arg_names": ["project_name", "example_id", "tags", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "langchain_core.tracers.context.tracing_v2_enabled", "name": "tracing_v2_enabled", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": ["project_name", "example_id", "tags", "client"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.client.Client", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tracing_v2_enabled", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.langchain.LangChainTracer", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "langchain_core.tracers.context.tracing_v2_enabled", "name": "tracing_v2_enabled", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": ["project_name", "example_id", "tags", "client"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.client.Client", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tracing_v2_enabled", "ret_type": {".class": "Instance", "args": ["langchain_core.tracers.langchain.LangChainTracer", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/context.py"}