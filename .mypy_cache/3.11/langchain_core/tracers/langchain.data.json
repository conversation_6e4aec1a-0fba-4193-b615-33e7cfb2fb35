{".class": "MypyFile", "_fullname": "langchain_core.tracers.langchain", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseMessage": {".class": "SymbolTableNode", "cross_ref": "langchain_core.messages.base.BaseMessage", "kind": "Gdef"}, "BaseTracer": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.base.BaseTracer", "kind": "Gdef"}, "ChatGenerationChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.outputs.chat_generation.ChatGenerationChunk", "kind": "Gdef"}, "Client": {".class": "SymbolTableNode", "cross_ref": "langsmith.client.Client", "kind": "Gdef"}, "GenerationChunk": {".class": "SymbolTableNode", "cross_ref": "langchain_core.outputs.generation.GenerationChunk", "kind": "Gdef"}, "LangChainTracer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.tracers.base.BaseTracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.langchain.LangChainTracer", "name": "Lang<PERSON>hainT<PERSON>r", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.langchain.LangChainTracer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_core.tracers.langchain", "mro": ["langchain_core.tracers.langchain.LangChainTracer", "langchain_core.tracers.base.BaseTracer", "langchain_core.tracers.core._TracerCore", "langchain_core.callbacks.base.BaseCallbackHandler", "abc.ABC", "langchain_core.callbacks.base.LLMManagerMixin", "langchain_core.callbacks.base.ChainManagerMixin", "langchain_core.callbacks.base.ToolManagerMixin", "langchain_core.callbacks.base.RetrieverManagerMixin", "langchain_core.callbacks.base.CallbackManagerMixin", "langchain_core.callbacks.base.RunManagerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "example_id", "project_name", "client", "tags", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "example_id", "project_name", "client", "tags", "kwargs"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", {".class": "UnionType", "items": ["uuid.UUID", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["langsmith.client.Client", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._get_tags", "name": "_get_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_tags of LangChainTracer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_llm_run_with_token_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "token", "run_id", "chunk", "parent_run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._llm_run_with_token_event", "name": "_llm_run_with_token_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "token", "run_id", "chunk", "parent_run_id"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "builtins.str", "uuid.UUID", {".class": "UnionType", "items": ["langchain_core.outputs.generation.GenerationChunk", "langchain_core.outputs.chat_generation.ChatGenerationChunk", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_llm_run_with_token_event of LangChainTracer", "ret_type": "langsmith.run_trees.RunTree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._llm_run_with_token_event", "name": "_llm_run_with_token_event", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "token", "run_id", "chunk", "parent_run_id"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "builtins.str", "uuid.UUID", {".class": "UnionType", "items": ["langchain_core.outputs.generation.GenerationChunk", "langchain_core.outputs.chat_generation.ChatGenerationChunk", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_llm_run_with_token_event of LangChainTracer", "ret_type": "langsmith.run_trees.RunTree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_on_chain_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_chain_end", "name": "_on_chain_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chain_end of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_chain_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_chain_error", "name": "_on_chain_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chain_error of Lang<PERSON>hainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_chain_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_chain_start", "name": "_on_chain_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chain_start of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_chat_model_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_chat_model_start", "name": "_on_chat_model_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chat_model_start of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_llm_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_llm_end", "name": "_on_llm_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_end of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_llm_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_llm_error", "name": "_on_llm_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_error of Lang<PERSON>hainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_llm_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_llm_start", "name": "_on_llm_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_start of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_retriever_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_retriever_end", "name": "_on_retriever_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_retriever_end of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_retriever_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_retriever_error", "name": "_on_retriever_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_retriever_error of Lang<PERSON>hainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_retriever_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_retriever_start", "name": "_on_retriever_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_retriever_start of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_tool_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_tool_end", "name": "_on_tool_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_tool_end of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_tool_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_tool_error", "name": "_on_tool_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_tool_error of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_tool_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._on_tool_start", "name": "_on_tool_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_tool_start of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_persist_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._persist_run", "name": "_persist_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_persist_run of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_persist_run_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._persist_run_single", "name": "_persist_run_single", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_persist_run_single of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_start_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._start_trace", "name": "_start_trace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_start_trace of <PERSON><PERSON><PERSON><PERSON>T<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_run_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer._update_run_single", "name": "_update_run_single", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update_run_single of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.client", "name": "client", "setter_type": null, "type": "langsmith.client.Client"}}, "example_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.example_id", "name": "example_id", "setter_type": null, "type": {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_run_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.get_run_url", "name": "get_run_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_run_url of LangChainTracer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "latest_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.latest_run", "name": "latest_run", "setter_type": null, "type": {".class": "UnionType", "items": ["langsmith.run_trees.RunTree", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "on_chat_model_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 5, 5, 5, 5, 4], "arg_names": ["self", "serialized", "messages", "run_id", "tags", "parent_run_id", "metadata", "name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.on_chat_model_start", "name": "on_chat_model_start", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 5, 5, 5, 5, 4], "arg_names": ["self", "serialized", "messages", "run_id", "tags", "parent_run_id", "metadata", "name", "kwargs"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["langchain_core.messages.base.BaseMessage"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "uuid.UUID", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["uuid.UUID", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_chat_model_start of LangChainTracer", "ret_type": "langsmith.run_trees.RunTree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "project_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.project_name", "name": "project_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "run_has_token_event_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.run_has_token_event_map", "name": "run_has_token_event_map", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "run_inline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.run_inline", "name": "run_inline", "setter_type": null, "type": "builtins.bool"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.tags", "name": "tags", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "wait_for_futures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.langchain.LangChainTracer.wait_for_futures", "name": "wait_for_futures", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.tracers.langchain.LangChainTracer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_for_futures of LangChainTracer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.langchain.LangChainTracer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.langchain.LangChainTracer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Retrying": {".class": "SymbolTableNode", "cross_ref": "tenacity.<PERSON>trying", "kind": "Gdef"}, "Run": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.schemas.Run", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_EXECUTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.langchain._EXECUTOR", "name": "_EXECUTOR", "setter_type": null, "type": {".class": "UnionType", "items": ["concurrent.futures.thread.ThreadPoolExecutor", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_LOGGED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "langchain_core.tracers.langchain._LOGGED", "name": "_LOGGED", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.langchain.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.langchain.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.langchain.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.langchain.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.langchain.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.langchain.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_executor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.langchain._get_executor", "name": "_get_executor", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_executor", "ret_type": "concurrent.futures.thread.ThreadPoolExecutor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "dumpd": {".class": "SymbolTableNode", "cross_ref": "langchain_core.load.dump.dumpd", "kind": "Gdef"}, "get_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.langchain.get_client", "name": "get_client", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_client", "ret_type": "langsmith.client.Client", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_runtime_environment": {".class": "SymbolTableNode", "cross_ref": "langchain_core.env.get_runtime_environment", "kind": "Gdef"}, "log_error_once": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["method", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.langchain.log_error_once", "name": "log_error_once", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["method", "exception"], "arg_types": ["builtins.str", "builtins.Exception"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "log_error_once", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "langchain_core.tracers.langchain.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ls_utils": {".class": "SymbolTableNode", "cross_ref": "langsmith.utils", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "retry_if_exception_type": {".class": "SymbolTableNode", "cross_ref": "tenacity.retry.retry_if_exception_type", "kind": "Gdef"}, "rt": {".class": "SymbolTableNode", "cross_ref": "langsmith.run_trees", "kind": "Gdef"}, "stop_after_attempt": {".class": "SymbolTableNode", "cross_ref": "tenacity.stop.stop_after_attempt", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "wait_exponential_jitter": {".class": "SymbolTableNode", "cross_ref": "tenacity.wait.wait_exponential_jitter", "kind": "Gdef"}, "wait_for_all_tracers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.langchain.wait_for_all_tracers", "name": "wait_for_all_tracers", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "wait_for_all_tracers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/langchain.py"}