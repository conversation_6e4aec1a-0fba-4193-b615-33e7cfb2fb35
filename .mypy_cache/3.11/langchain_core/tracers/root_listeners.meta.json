{"data_mtime": 1752086943, "dep_lines": [6, 11, 12, 3, 4, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.runnables.config", "langchain_core.tracers.base", "langchain_core.tracers.schemas", "collections.abc", "typing", "uuid", "builtins", "_frozen_importlib", "abc", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.callbacks.manager", "langchain_core.runnables", "langchain_core.tracers.core", "langsmith", "langsmith.run_trees", "langsmith.schemas", "pydantic", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils"], "hash": "95ab82d0bf755082abf1991a0835e82273c46ec8", "id": "langchain_core.tracers.root_listeners", "ignore_all": true, "interface_hash": "462df24020ffb0e961c9be563d85ff05893cad19", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/root_listeners.py", "plugin_data": null, "size": 4672, "suppressed": [], "version_id": "1.16.1"}