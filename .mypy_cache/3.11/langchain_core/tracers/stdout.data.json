{".class": "MypyFile", "_fullname": "langchain_core.tracers.stdout", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseTracer": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.base.BaseTracer", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConsoleCallbackHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.tracers.stdout.FunctionCallbackHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.stdout.ConsoleCallbackHandler", "name": "ConsoleCallbackHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.stdout.ConsoleCallbackHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_core.tracers.stdout", "mro": ["langchain_core.tracers.stdout.ConsoleCallbackHandler", "langchain_core.tracers.stdout.FunctionCallbackHandler", "langchain_core.tracers.base.BaseTracer", "langchain_core.tracers.core._TracerCore", "langchain_core.callbacks.base.BaseCallbackHandler", "abc.ABC", "langchain_core.callbacks.base.LLMManagerMixin", "langchain_core.callbacks.base.ChainManagerMixin", "langchain_core.callbacks.base.ToolManagerMixin", "langchain_core.callbacks.base.RetrieverManagerMixin", "langchain_core.callbacks.base.CallbackManagerMixin", "langchain_core.callbacks.base.RunManagerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.ConsoleCallbackHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["langchain_core.tracers.stdout.ConsoleCallbackHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConsoleCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.stdout.ConsoleCallbackHandler.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.stdout.ConsoleCallbackHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.stdout.ConsoleCallbackHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionCallbackHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.tracers.base.BaseTracer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler", "name": "FunctionCallbackHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_core.tracers.stdout", "mro": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langchain_core.tracers.base.BaseTracer", "langchain_core.tracers.core._TracerCore", "langchain_core.callbacks.base.BaseCallbackHandler", "abc.ABC", "langchain_core.callbacks.base.LLMManagerMixin", "langchain_core.callbacks.base.ChainManagerMixin", "langchain_core.callbacks.base.ToolManagerMixin", "langchain_core.callbacks.base.RetrieverManagerMixin", "langchain_core.callbacks.base.CallbackManagerMixin", "langchain_core.callbacks.base.RunManagerMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "function", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "function", "kwargs"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_chain_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_chain_end", "name": "_on_chain_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chain_end of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_chain_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_chain_error", "name": "_on_chain_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chain_error of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_chain_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_chain_start", "name": "_on_chain_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_chain_start of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_llm_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_llm_end", "name": "_on_llm_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_end of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_llm_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_llm_error", "name": "_on_llm_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_error of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_llm_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_llm_start", "name": "_on_llm_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_llm_start of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_tool_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_tool_end", "name": "_on_tool_end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_tool_end of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_tool_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_tool_error", "name": "_on_tool_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_tool_error of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_on_tool_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._on_tool_start", "name": "_on_tool_start", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_on_tool_start of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_persist_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler._persist_run", "name": "_persist_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_persist_run of FunctionCallbackHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler.function_callback", "name": "function_callback", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_breadcrumbs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler.get_breadcrumbs", "name": "get_breadcrumbs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_breadcrumbs of FunctionCallbackHandler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_parents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler.get_parents", "name": "get_parents", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run"], "arg_types": ["langchain_core.tracers.stdout.FunctionCallbackHandler", "langsmith.run_trees.RunTree"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_parents of FunctionCallbackHandler", "ret_type": {".class": "Instance", "args": ["langsmith.run_trees.RunTree"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler.name", "name": "name", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.tracers.stdout.FunctionCallbackHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.tracers.stdout.FunctionCallbackHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MILLISECONDS_IN_SECOND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "langchain_core.tracers.stdout.MILLISECONDS_IN_SECOND", "name": "MILLISECONDS_IN_SECOND", "setter_type": null, "type": "builtins.int"}}, "Run": {".class": "SymbolTableNode", "cross_ref": "langchain_core.tracers.schemas.Run", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.stdout.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.stdout.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.stdout.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.stdout.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.stdout.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.tracers.stdout.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "elapsed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["run"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.stdout.elapsed", "name": "elapsed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["run"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "elapsed", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bolded_text": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.input.get_bolded_text", "kind": "Gdef"}, "get_colored_text": {".class": "SymbolTableNode", "cross_ref": "langchain_core.utils.input.get_colored_text", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "try_json_stringify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "langchain_core.tracers.stdout.try_json_stringify", "name": "try_json_stringify", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "fallback"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "try_json_stringify", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/tracers/stdout.py"}