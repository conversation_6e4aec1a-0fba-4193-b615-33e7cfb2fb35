{"data_mtime": 1752086943, "dep_lines": [29, 33, 34, 35, 36, 26, 24, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 5, 5, 5, 30, 30], "dependencies": ["langchain_core.outputs.chat_generation", "langchain_core.outputs.chat_result", "langchain_core.outputs.generation", "langchain_core.outputs.llm_result", "langchain_core.outputs.run_info", "langchain_core._import_utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "c276d8169062b21f4855ef3c8ed7efb7524a327a", "id": "langchain_core.outputs", "ignore_all": true, "interface_hash": "6066db8f50125641d15172bbe32487096b5dc715", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/outputs/__init__.py", "plugin_data": null, "size": 2133, "suppressed": [], "version_id": "1.16.1"}