{"data_mtime": 1752086943, "dep_lines": [11, 12, 10, 3, 5, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.outputs.generation", "langchain_core.utils._merge", "langchain_core.messages", "__future__", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.messages.base", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._model_construction", "pydantic.functional_validators", "pydantic.main", "pydantic_core", "pydantic_core.core_schema", "types"], "hash": "5979c9af78918bd7930dcf8a89a570843cae7b64", "id": "langchain_core.outputs.chat_generation", "ignore_all": true, "interface_hash": "c399a02782de952cbe81e608a89e33eeb2a6d823", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/outputs/chat_generation.py", "plugin_data": null, "size": 4350, "suppressed": [], "version_id": "1.16.1"}