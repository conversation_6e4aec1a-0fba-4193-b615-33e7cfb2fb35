{"data_mtime": 1752086943, "dep_lines": [19, 21, 17, 18, 19, 20, 25, 27, 28, 3, 5, 6, 7, 8, 15, 503, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 25, 25, 25, 5, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.load.load", "langchain_core.vectorstores.utils", "langchain_core._api", "langchain_core.documents", "langchain_core.load", "langchain_core.vectorstores", "collections.abc", "langchain_core.embeddings", "langchain_core.indexing", "__future__", "json", "uuid", "pathlib", "typing", "typing_extensions", "numpy", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "langchain_core._api.deprecation", "langchain_core.documents.base", "langchain_core.embeddings.embeddings", "langchain_core.indexing.base", "langchain_core.load.serializable", "langchain_core.vectorstores.base", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "295744e12099fb6f5ce00aa0981c48b015e29b10", "id": "langchain_core.vectorstores.in_memory", "ignore_all": true, "interface_hash": "9418badb7c1d1b52e209d94108352f0e0ca584e3", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/vectorstores/in_memory.py", "plugin_data": null, "size": 18076, "suppressed": [], "version_id": "1.16.1"}