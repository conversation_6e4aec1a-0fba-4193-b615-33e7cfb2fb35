{"data_mtime": 1752086944, "dep_lines": [43, 48, 41, 42, 46, 52, 22, 24, 25, 26, 27, 28, 29, 38, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 5, 25, 20, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["langchain_core.runnables.config", "langchain_core.callbacks.manager", "langchain_core.embeddings", "langchain_core.retrievers", "collections.abc", "langchain_core.documents", "__future__", "logging", "math", "warnings", "abc", "itertools", "typing", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "annotated_types", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.documents.base", "langchain_core.embeddings.embeddings", "langchain_core.load", "langchain_core.load.serializable", "langchain_core.runnables", "langchain_core.runnables.base", "pydantic._internal", "pydantic._internal._decorators", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.config", "pydantic.fields", "pydantic.functional_validators", "pydantic.main", "pydantic.types", "pydantic_core", "pydantic_core.core_schema", "re"], "hash": "822c641dee0ba9d1af17ffe2199c5e149d66be61", "id": "langchain_core.vectorstores.base", "ignore_all": true, "interface_hash": "9ab09ea25d6886317e56e8d44252f6a172a6c69d", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/vectorstores/base.py", "plugin_data": null, "size": 42025, "suppressed": [], "version_id": "1.16.1"}