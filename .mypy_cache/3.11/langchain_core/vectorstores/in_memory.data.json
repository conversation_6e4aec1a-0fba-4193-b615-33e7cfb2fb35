{".class": "MypyFile", "_fullname": "langchain_core.vectorstores.in_memory", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "langchain_core.documents.base.Document", "kind": "Gdef"}, "Embeddings": {".class": "SymbolTableNode", "cross_ref": "langchain_core.embeddings.embeddings.Embeddings", "kind": "Gdef"}, "InMemoryVectorStore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["langchain_core.vectorstores.base.VectorStore"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "name": "InMemoryVectorStore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "langchain_core.vectorstores.in_memory", "mro": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "langchain_core.vectorstores.base.VectorStore", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "embedding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "embedding"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "langchain_core.embeddings.embeddings.Embeddings"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of InMemoryVectorStore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_similarity_search_with_score_by_vector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "embedding", "k", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore._similarity_search_with_score_by_vector", "name": "_similarity_search_with_score_by_vector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "embedding", "k", "filter"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.documents.base.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_similarity_search_with_score_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aadd_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "documents", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.aadd_documents", "name": "aadd_documents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "documents", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aadd_documents of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.aadd_documents", "name": "aadd_documents", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "documents", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aadd_documents of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "documents", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.add_documents", "name": "add_documents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "documents", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_documents of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.add_documents", "name": "add_documents", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "documents", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_documents of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "adelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.adelete", "name": "adelete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adelete of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.adelete", "name": "adelete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adelete of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "afrom_texts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.afrom_texts", "name": "afrom_texts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_core.vectorstores.in_memory.InMemoryVectorStore"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "langchain_core.embeddings.embeddings.Embeddings", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "afrom_texts of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "langchain_core.vectorstores.in_memory.InMemoryVectorStore"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.afrom_texts", "name": "afrom_texts", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_core.vectorstores.in_memory.InMemoryVectorStore"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "langchain_core.embeddings.embeddings.Embeddings", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "afrom_texts of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "langchain_core.vectorstores.in_memory.InMemoryVectorStore"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "aget_by_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.aget_by_ids", "name": "aget_by_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aget_by_ids of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.aget_by_ids", "name": "aget_by_ids", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aget_by_ids of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "amax_marginal_relevance_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "query", "k", "fetch_k", "lambda_mult", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.amax_marginal_relevance_search", "name": "amax_marginal_relevance_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "query", "k", "fetch_k", "lambda_mult", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "amax_marginal_relevance_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.amax_marginal_relevance_search", "name": "amax_marginal_relevance_search", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "query", "k", "fetch_k", "lambda_mult", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "amax_marginal_relevance_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "asimilarity_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.asimilarity_search", "name": "asimilarity_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asimilarity_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.asimilarity_search", "name": "asimilarity_search", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asimilarity_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "asimilarity_search_by_vector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "embedding", "k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.asimilarity_search_by_vector", "name": "asimilarity_search_by_vector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "embedding", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asimilarity_search_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.asimilarity_search_by_vector", "name": "asimilarity_search_by_vector", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "embedding", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asimilarity_search_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "asimilarity_search_with_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.asimilarity_search_with_score", "name": "asimilarity_search_with_score", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asimilarity_search_with_score of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.asimilarity_search_with_score", "name": "asimilarity_search_with_score", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "asimilarity_search_with_score of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "aupsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": [null, null, "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.aupsert", "name": "aupsert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": [null, null, "_kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aupsert of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.indexing.base.UpsertResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.aupsert", "name": "aupsert", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": [null, null, "_kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "aupsert of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.indexing.base.UpsertResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of InMemoryVectorStore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "ids", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of InMemoryVectorStore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.dump", "name": "dump", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dump of InMemoryVectorStore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.embedding", "name": "embedding", "setter_type": null, "type": "langchain_core.embeddings.embeddings.Embeddings"}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of InMemoryVectorStore", "ret_type": "langchain_core.embeddings.embeddings.Embeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of InMemoryVectorStore", "ret_type": "langchain_core.embeddings.embeddings.Embeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_texts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.from_texts", "name": "from_texts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_core.vectorstores.in_memory.InMemoryVectorStore"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "langchain_core.embeddings.embeddings.Embeddings", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_texts of InMemoryVectorStore", "ret_type": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.from_texts", "name": "from_texts", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["cls", "texts", "embedding", "metadatas", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_core.vectorstores.in_memory.InMemoryVectorStore"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "langchain_core.embeddings.embeddings.Embeddings", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_texts of InMemoryVectorStore", "ret_type": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_by_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.get_by_ids", "name": "get_by_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_by_ids of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.get_by_ids", "name": "get_by_ids", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_by_ids of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "path", "embedding", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "path", "embedding", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_core.vectorstores.in_memory.InMemoryVectorStore"}, "builtins.str", "langchain_core.embeddings.embeddings.Embeddings", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load of InMemoryVectorStore", "ret_type": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.load", "name": "load", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["cls", "path", "embedding", "kwargs"], "arg_types": [{".class": "TypeType", "item": "langchain_core.vectorstores.in_memory.InMemoryVectorStore"}, "builtins.str", "langchain_core.embeddings.embeddings.Embeddings", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load of InMemoryVectorStore", "ret_type": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "max_marginal_relevance_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "query", "k", "fetch_k", "lambda_mult", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.max_marginal_relevance_search", "name": "max_marginal_relevance_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "query", "k", "fetch_k", "lambda_mult", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "max_marginal_relevance_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.max_marginal_relevance_search", "name": "max_marginal_relevance_search", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "query", "k", "fetch_k", "lambda_mult", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "max_marginal_relevance_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "max_marginal_relevance_search_by_vector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["self", "embedding", "k", "fetch_k", "lambda_mult", "filter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.max_marginal_relevance_search_by_vector", "name": "max_marginal_relevance_search_by_vector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["self", "embedding", "k", "fetch_k", "lambda_mult", "filter", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.documents.base.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "max_marginal_relevance_search_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.max_marginal_relevance_search_by_vector", "name": "max_marginal_relevance_search_by_vector", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["self", "embedding", "k", "fetch_k", "lambda_mult", "filter", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.documents.base.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "max_marginal_relevance_search_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "similarity_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search", "name": "similarity_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search", "name": "similarity_search", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "similarity_search_by_vector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "embedding", "k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search_by_vector", "name": "similarity_search_by_vector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "embedding", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search_by_vector", "name": "similarity_search_by_vector", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "embedding", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "similarity_search_with_score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search_with_score", "name": "similarity_search_with_score", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search_with_score of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search_with_score", "name": "similarity_search_with_score", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "query", "k", "kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search_with_score of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "similarity_search_with_score_by_vector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "embedding", "k", "filter", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.similarity_search_with_score_by_vector", "name": "similarity_search_with_score_by_vector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "embedding", "k", "filter", "_kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["langchain_core.documents.base.Document"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "similarity_search_with_score_by_vector of InMemoryVectorStore", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["langchain_core.documents.base.Document", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "store": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.store", "name": "store", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "upsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": [null, null, "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.upsert", "name": "upsert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": [null, null, "_kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert of InMemoryVectorStore", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.indexing.base.UpsertResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.upsert", "name": "upsert", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": [null, null, "_kwargs"], "arg_types": ["langchain_core.vectorstores.in_memory.InMemoryVectorStore", {".class": "Instance", "args": ["langchain_core.documents.base.Document"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upsert of InMemoryVectorStore", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "langchain_core.indexing.base.UpsertResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "langchain_core.vectorstores.in_memory.InMemoryVectorStore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "langchain_core.vectorstores.in_memory.InMemoryVectorStore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UpsertResponse": {".class": "SymbolTableNode", "cross_ref": "langchain_core.indexing.base.UpsertResponse", "kind": "Gdef"}, "VectorStore": {".class": "SymbolTableNode", "cross_ref": "langchain_core.vectorstores.base.VectorStore", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.vectorstores.in_memory.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.vectorstores.in_memory.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.vectorstores.in_memory.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.vectorstores.in_memory.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.vectorstores.in_memory.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "langchain_core.vectorstores.in_memory.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cosine_similarity": {".class": "SymbolTableNode", "cross_ref": "langchain_core.vectorstores.utils._cosine_similarity", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "langchain_core._api.deprecation.deprecated", "kind": "Gdef"}, "dumpd": {".class": "SymbolTableNode", "cross_ref": "langchain_core.load.dump.dumpd", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load": {".class": "SymbolTableNode", "cross_ref": "langchain_core.load.load.load", "kind": "Gdef"}, "maximal_marginal_relevance": {".class": "SymbolTableNode", "cross_ref": "langchain_core.vectorstores.utils.maximal_marginal_relevance", "kind": "Gdef"}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/vectorstores/in_memory.py"}