{"data_mtime": 1752086943, "dep_lines": [10, 19, 20, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "langchain_core.exceptions", "langchain_core.runnables", "abc", "typing", "builtins", "_frozen_importlib", "_typeshed", "concurrent", "concurrent.futures", "concurrent.futures._base", "langchain_core.callbacks", "langchain_core.callbacks.base", "langchain_core.runnables.config", "uuid"], "hash": "bfc7e981df074d9cc47175c0b4d5d831f92e793b", "id": "langchain_core.stores", "ignore_all": true, "interface_hash": "112a2ab0ab7e1fb86e33d55b2c77cdcc2c2a70c3", "mtime": 1751599593, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Proyectos/qak/.venv/lib/python3.11/site-packages/langchain_core/stores.py", "plugin_data": null, "size": 10819, "suppressed": [], "version_id": "1.16.1"}